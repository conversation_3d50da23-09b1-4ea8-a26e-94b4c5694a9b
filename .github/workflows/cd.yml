name: Releaser
on:
  release:
    types:
      - published
jobs:
  build:
    name: deploy
    runs-on: ubuntu-latest
    steps:
      - name: deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          port: ${{ secrets.PORT }}
          script_stop: true
          script: |
            cd kalkan-oto/backend
            git fetch --all
            git checkout ${{ github.event.release.tag_name }}
            docker compose -f docker-compose.yml stop kalkan
            docker compose -f docker-compose.yml rm -f kalkan
            docker compose -f docker-compose.yml up -d --build kalkan
            cd ../frontend
            # Node.js v22'yi nvm ile kur ve kullan
            curl -sL https://deb.nodesource.com/setup_22.x | bash -
            source ~/.nvm/nvm.sh  # nvm'yi yükle
            nvm install 22  # v22'yi kur
            nvm use 22  # v22 sürümünü aktif et
            node --version  # Versiyonu doğrula
            npm install
            npm run build