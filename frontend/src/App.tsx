import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { RootState } from "./app/store";
import SingleStore from "./views/Authenticated/Store/SingleStore";

import LoginPage from "./views/Unauthenticated/login/Login";
import Dashboard from "./views/Authenticated/Dashboard/Dashboard";
import Store from "./views/Authenticated/Store/Store";
import Item from "./views/Authenticated/Item/Item";
import Trash from "./views/Authenticated/Trash/Trash";

import SideBar from "./component/SideBar/SideBar";
import { Outlet } from "react-router-dom";
import VichleSingle from "./views/Authenticated/Vichle/VichleSingle";
import StoreLogin from "./views/Unauthenticated/StoresLogin/StoreLogin";
import StoreDashboard from "./views/Unauthenticated/StoresLogin/StoreDashboard";
import StoreVechile from "./views/Unauthenticated/StoresLogin/StoreVechile";

function App() {
  // Access the authentication token from Redux store
  const token = useSelector((state: RootState) => state.user.token);
  const store_token = useSelector((state: RootState) => state.store.token);

  return (
    <BrowserRouter>
      <Routes>
        <Route
          path="/stores/:name"
          element={!store_token ? <StoreLogin /> : <StoreDashboard />}
        />
        <Route
          path="/stores/dashboard/:name"
          element={
            store_token ? (
              <StoreDashboard />
            ) : (
              <Navigate to="/stores/:name" replace />
            )
          }
        />
        <Route
          path="/stores/vechile/:id"
          element={
            store_token ? (
              <StoreVechile />
            ) : (
              <Navigate to="/stores/:id" replace />
            )
          }
        />
        <Route path="/auth" element={<AuthLayout />}>
          <Route
            path="login"
            element={
              token ? <Navigate to="/dashboard" replace /> : <LoginPage />
            }
          />
          <Route path="*" element={<Navigate to="/auth/login" replace />} />
        </Route>

        <Route path="/" element={<AuthLayout />}>
          <Route
            path="/"
            element={
              token ? <Navigate to="/dashboard" replace /> : <LoginPage />
            }
          />
        </Route>

        <Route
          path="/"
          element={
            token ? (
              <AuthenticatedLayout />
            ) : (
              <Navigate to="/auth/login" replace />
            )
          }
        >
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="store" element={<Store />} />
          <Route path="store/:id" element={<SingleStore />} />
          <Route path="item" element={<Item />} />
          <Route path="trash" element={<Trash />} />
          <Route path="vechile/:id" element={<VichleSingle />} />
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Route>

        {/* Global Catch-all Route */}
        <Route
          path="*"
          element={
            <Navigate
              to={
                token
                  ? token
                    ? "/dashboard"
                    : "/auth/login"
                  : store_token
                  ? "/stores/dashboard/:name"
                  : "/stores/:name"
              }
              replace
            />
          }
        />
      </Routes>
    </BrowserRouter>
  );
}

/**
 * Layout component for unauthenticated routes.
 * Does **not** include the SideBar.
 */
function AuthLayout() {
  return <Outlet />;
}

/**
 * Layout component for authenticated routes.
 * Includes the SideBar and renders child components within it.
 */
function AuthenticatedLayout() {
  return (
    <SideBar>
      <Outlet />
    </SideBar>
  );
}

export default App;
