import React, { ReactNode, useState } from "react";

interface TooltipProps {
  content: ReactNode | string;
  children: ReactNode;
  position?: "top" | "bottom" | "left" | "right";
}

const Tooltip: React.FC<TooltipProps> = ({
  content,
  children,
  position = "top",
}) => {
  const [isVisible, setIsVisible] = useState(false);

  const tooltipPositionClasses = {
    top: "bottom-full left-1/2 transform -translate-x-1/2 mb-2",
    bottom: "top-full left-1/2 transform -translate-x-1/2 mt-2",
    left: "right-full top-1/2 transform -translate-y-1/2 mr-2",
    right: "left-full top-1/2 transform -translate-y-1/2 ml-2",
  };

  return (
    <div className="relative inline-block">
      {/* Tooltip Content */}
      {isVisible && (
        <div
          className={`absolute z-10 px-2 py-1 text-white bg-gray-800 rounded-lg shadow-lg ${tooltipPositionClasses[position]} z-40`}
        >
          {content}
        </div>
      )}
      {/* Target Element */}
      <div
        className="inline-block"
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
        onFocus={() => setIsVisible(true)}
        onBlur={() => setIsVisible(false)}
      >
        {children}
      </div>
    </div>
  );
};

export default Tooltip;
