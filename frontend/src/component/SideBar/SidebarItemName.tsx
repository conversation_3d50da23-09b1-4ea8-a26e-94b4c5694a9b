import React from "react";
import { SideBarItemProps } from "../../types/sidbarItemProps";
import { useLocation } from "react-router";

function classNames(...classes: any) {
  return classes.filter(Boolean).join(" ");
}

const SideBarItemName: React.FC<SideBarItemProps> = ({
  handleChangePage,
  item,
}) => {
  const location = useLocation();
  const isActive = location.pathname.includes(item.href);

  return (
    <a
      onClick={handleChangePage}
      href={item.href}
      aria-current={isActive ? "page" : undefined}
      className={classNames(
        isActive
          ? "bg-neutral-900 text-rose-200 border-l-4 border-rose-200"
          : "text-white hover:bg-neutral-900 hover:text-rose-200 hover:border-l-2 hover:border-rose-200",
        "group flex w-full text-sm font-semibold leading-6 py-3.5 items-center justify-start gap-x-3 px-2"
      )}
    >
      <item.icon
        aria-hidden="true"
        className={classNames(
          isActive ? "text-rose-200" : "text-white group-hover:text-rose-200",
          "h-6 w-6 shrink-0"
        )}
      />
      {item.name}
    </a>
  );
};

export default SideBarItemName;
