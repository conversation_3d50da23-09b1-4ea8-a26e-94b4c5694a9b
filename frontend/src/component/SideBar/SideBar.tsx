import React, { useState } from "react";
import {
  Bars3Icon,
  XMarkIcon,
  MegaphoneIcon,
  GiftIcon,
  CreditCardIcon,
  ArrowLeftOnRectangleIcon,
} from "@heroicons/react/24/outline";
import { Link } from "react-router-dom";
import { useDispatch } from "react-redux";
import { logout } from "../../slices/userSlice";
import { FaCar, FaTrash } from "react-icons/fa";

interface SideBarProps {
  children: React.ReactNode;
}

function SideBar({ children }: SideBarProps) {
  const [isSidebarExpanded, setIsSidebarExpanded] = useState<boolean>(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState<boolean>(false);
  const dispatch = useDispatch();

  const navigation = [
    { name: "Home", href: "/dashboard", icon: MegaphoneIcon },
    { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", href: "/store", icon: GiftIcon },
    { name: "Par<PERSON>lar", href: "/item", icon: CreditCardIcon },
    { name: "<PERSON><PERSON><PERSON><PERSON>", href: "/trash", icon: FaTrash },
  ];

  return (
    <div className="flex w-full">
      {/* Sidebar for large screens */}
      <div
        className={`hidden lg:flex lg:flex-col lg:fixed lg:inset-y-0 transition-all duration-300 ${
          isSidebarExpanded ? "lg:w-64" : "lg:w-20"
        } bg-gray-800 text-white`}
        onMouseEnter={() => setIsSidebarExpanded(true)}
        onMouseLeave={() => setIsSidebarExpanded(false)}
      >
        <div className="flex items-center justify-center h-16">
          <FaCar className="ml-2" size={25} />
        </div>
        <nav className="flex-1 space-y-2 p-4">
          {navigation.map((item) => (
            <Link
              key={item.name}
              to={item.href}
              className="flex items-center gap-4 px-4 py-2 rounded-lg hover:bg-gray-700 transition-all"
            >
              <item.icon className="h-6 w-6 flex-shrink-0" />
              <span
                className={`whitespace-nowrap transition-opacity duration-300 ${
                  isSidebarExpanded ? "opacity-100" : "opacity-0 hidden"
                }`}
              >
                {item.name}
              </span>
            </Link>
          ))}
        </nav>
        <div className="p-4">
          <Link
            to="/logout"
            className="flex items-center gap-4 px-4 py-2 rounded-lg hover:bg-red-600 transition-all"
            onClick={() => dispatch(logout())}
          >
            <ArrowLeftOnRectangleIcon className="h-6 w-6 flex-shrink-0" />
            <span
              className={`transition-opacity duration-300 ${
                isSidebarExpanded ? "opacity-100" : "opacity-0 hidden"
              }`}
            >
              Logout
            </span>
          </Link>
        </div>
      </div>

      {/* Mobile Sidebar */}
      {isMobileMenuOpen && (
        <div className="lg:hidden fixed inset-0 z-40 bg-black bg-opacity-50">
          <div className="relative bg-gray-800 w-64 h-full">
            <button
              className="absolute top-4 right-4 text-white"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
            <nav className="mt-10 space-y-2 p-4">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className="flex items-center gap-4 px-4 py-2 rounded-lg hover:bg-gray-700 transition-all"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <item.icon className="h-6 w-6 flex-shrink-0" />
                  <span>{item.name}</span>
                </Link>
              ))}
              <Link
                to="/logout"
                className="flex items-center gap-4 px-4 py-2 rounded-lg hover:bg-red-600 transition-all"
                onClick={() => {
                  setIsMobileMenuOpen(false);
                  dispatch(logout());
                }}
              >
                <ArrowLeftOnRectangleIcon className="h-6 w-6 flex-shrink-0" />
                <span>Logout</span>
              </Link>
            </nav>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div
        className={`w-full transition-padding duration-300 ease-in-out ${
          isSidebarExpanded ? "lg:pl-64" : "lg:pl-20"
        }`}
      >
        <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <button
            type="button"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="lg:hidden -m-2.5 p-2.5 text-gray-700"
          >
            <Bars3Icon aria-hidden="true" className="h-6 w-6" />
          </button>
          <div className="flex flex-1 justify-between items-center gap-x-4 self-stretch lg:gap-x-6">
            <div className="flex flex-row justify-center items-center gap-x-4 lg:gap-x-6"></div>
            <div className="flex items-center gap-x-2 lg:gap-x-6 flex-wrap justify-end"></div>
          </div>
        </div>
        <div className="p-4">{children}</div>
      </div>
    </div>
  );
}

export default SideBar;
