import React from "react";
import { SideBarItemProps } from "../../types/sidbarItemProps";
import { useLocation } from "react-router";

function classNames(...classes: any) {
  return classes.filter(Boolean).join(" ");
}

const SideBarItem: React.FC<SideBarItemProps> = ({
  handleChangePage,
  item,
}) => {
  const location = useLocation();
  return (
    <a
      onClick={handleChangePage}
      href={item.href}
      className={classNames(
        location.pathname.includes(item.href)
          ? "bg-neutral-900  text-rose-200 border-l-2 border-rose-200"
          : "text-white hover:bg-neutral-900 hover:text-rose-200 hover:border-l-2 hover:border-rose-200",
        "group flex w-full text-sm font-semibold leading-6  py-3.5 items-center justify-start gap-x-6 px-6 hover:text-rose-200"
      )}
    >
      <item.icon
        aria-hidden="true"
        className={classNames(
          location.pathname.includes(item.href)
            ? "text-rose-200"
            : "text-white group-hover:text-rose-200",
          "h-6 w-6 shrink-0"
        )}
      />
      <span className="ml-2 whitespace-nowrap opacity-100 transition-opacity duration-300">
        {item.name}
      </span>
    </a>
  );
};

export default SideBarItem;
