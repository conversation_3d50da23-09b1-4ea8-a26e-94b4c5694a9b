import Modal from "../Modal/Modal";
import { MdOutlineClose } from "react-icons/md";

interface IConfirmModalProps {
  onClose?: () => void;
  confirmOpen?: boolean;
  setConfirmOpen?: (value: boolean) => void;
  dataID?: any;
  handler: (id: string) => void;
}

const ConfirmModal: React.FC<IConfirmModalProps> = ({
  confirmOpen,
  onClose,
  dataID,
  handler,
}) => {
  return (
    confirmOpen && (
      <div className="flex h-full w-full items-center justify-center">
        <Modal
          classname="fixed inset-0 bg-gray-800 bg-opacity-50 p-3 flex items-center justify-center z-40"
          classname2="relative bg-white rounded-3xl w-full md:w-1/3 py-3"
          onClick={onClose}
        >
          <div className="w-full flex flex-col h-full">
            <div className="flex flex-col justify-between w-full h-full items-start p-8">
              <div className="w-full">
                <div className="flex flex-row w-full justify-between items-center mb-4">
                  <h2 className="text-lg font-bold text-center">
                    Silmek İstediğine Emin Misin ?
                  </h2>
                  <MdOutlineClose
                    className="cursor-pointer"
                    size={22}
                    onClick={onClose}
                  />
                </div>
              </div>
            </div>
            <div className="w-full flex  mt-4 gap-3 px-5">
              <button
                onClick={() => handler(dataID)}
                className=" w-full bg-blue-500 text-white px-4 py-2 rounded-lg hover:opacity-80"
              >
                Sil
              </button>
              <button
                onClick={onClose}
                className="w-full bg-red-500 text-white px-4 py-2 rounded-lg hover:opacity-80"
              >
                İptal
              </button>
            </div>
          </div>
        </Modal>
      </div>
    )
  );
};

export default ConfirmModal;
