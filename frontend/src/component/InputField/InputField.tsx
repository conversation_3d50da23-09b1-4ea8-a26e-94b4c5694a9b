import React from "react";

interface InputFieldProps {
  id?: string;
  label?: string;
  type?: string;
  value?: string;
  onChange: React.Dispatch<React.SetStateAction<any>>;
  error?: boolean | any;
  errorMessage?: string | any;
  required?: boolean;
  textArea?: boolean;
}

const InputField: React.FC<InputFieldProps> = ({
  id,
  label,
  type = "text",
  value,
  onChange,
  error = false,
  errorMessage = "error message",
  required,
  textArea,
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };
  return (
    <div className="relative w-full">
      {textArea ? (
        <textarea
          id={id}
          value={value}
          onChange={handleTextChange}
          maxLength={1000}
          className={`form-input bg-form-input-bg rounded-xl pt-6 mb-1 w-full ${
            error ? "border-error focus:border-error" : "border-none"
          } focus:ring-0 focus:outline-none transition-all duration-200 peer`}
        />
      ) : (
        <input
          id={id}
          type={type}
          value={value}
          onChange={handleChange}
          className={`form-input bg-form-input-bg rounded-xl pt-6 mb-1 w-full ${
            error ? "border-error focus:border-error" : "border-none"
          } focus:ring-0 focus:outline-none transition-all duration-200 peer`}
        />
      )}
      <label
        htmlFor={id}
        className={`absolute left-4 top-4 transition-all duration-200 transform ${
          value && value.length > 0
            ? "-translate-y-3 text-xs text-gray-600"
            : "text-base text-gray-400"
        } peer-focus:-translate-y-3 peer-focus:text-xs peer-focus:text-gray-600`}
      >
        {label}
        {required && "*"}
      </label>
      {error && <p className="text-error font-bold">{errorMessage}</p>}
    </div>
  );
};

export {};

export default InputField;
