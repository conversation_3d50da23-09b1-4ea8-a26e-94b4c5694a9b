export interface IVechile {
  id: string;
  CreatedAt: string;
  UpdatedAt: string;
  DeletedAt: string | null;
  updated_by: string;
  updated_date: string | null;
  deleted_by: string;
  deleted_date: string | null;
  maked_by: string;
  person_name: string;
  phone: string;
  plate_number: string;
  store_id: string;
  total_payment: number;
  remaining: number;
  items: string[];
}
export interface VehicleItem {
  CreatedAt: string;
  UpdatedAt: string;
  DeletedAt?: string | null;
  id: string;
  is_paid: number;
  item_id: string;
  maked_by: string;
  name: string;
  note: string;
  price: number;
  quantity: number;
  updated_by: string;
  updated_date?: string | null;
  vehicle_id: string;
  deleted_by: string;
  deleted_date?: string | null;
}

export interface StoreData {
  plate_number: string;
  phone: string;
  store_id: string;
  person_name: string;
}
export interface CreateVehicleItem {
  vehicle_id: string; // vehicle_id
  item_id: string | null; // item_id
  quantity: number; // quantity
  price: string; // price
  note: string; // note
  isPaid: number; // 1: paid, 2: not paid
  name: string;
}

export interface UpdateVehicleItem {
  quantity: number; // quantity
  price: string; // price
  note: string; // note
  is_paid: number; // 1: paid, 2: not paid
  name: string;
}
export interface UpdateVehicle {
  plate_number: string;
  phone: string;
  person_name: string;
}
