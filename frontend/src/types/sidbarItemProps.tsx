import {
  ComponentType,
  ForwardRefExoticComponent,
  RefAttributes,
  SVGProps,
} from "react";

export interface SideBarItemProps {
  handleChangePage: (event: React.MouseEvent<HTMLAnchorElement>) => void;
  item: {
    href: string;
    icon:
      | ComponentType<SVGProps<SVGSVGElement>>
      | ForwardRefExoticComponent<
          SVGProps<SVGSVGElement> & RefAttributes<SVGSVGElement>
        >;
    name?: string;
    desc?: string;
  };
}
