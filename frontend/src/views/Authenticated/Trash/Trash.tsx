import React, { useEffect, useState, useCallback } from "react";
import { FaSearch, FaTrashRestore } from "react-icons/fa";
import { getStoresWithDeletedVehicles, getDeletedVehiclesByStoreId, restoreVehicle } from "../../../services/StoresServices/store";
import { toast } from "react-toastify";

// Define the type for our debounced function
interface DebouncedFunction<T extends (...args: any[]) => any> {
  (...args: Parameters<T>): void;
  clear: () => void;
}

// Debounce function to limit API calls
const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): DebouncedFunction<T> => {
  let timeout: NodeJS.Timeout;

  const debounced = (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };

  // Add a clear method to cancel pending executions
  debounced.clear = () => {
    clearTimeout(timeout);
  };

  return debounced;
};

interface Store {
  id: string;
  name: string;
  slug_name: string;
  address: string;
  phone: string;
  CreatedAt: string;
  deleted_vehicles_count: number;
  remaining_amount: number;
  total_amount: number;
  paid_amount: number;
}

interface Vehicle {
  id: string;
  person_name: string;
  phone: string;
  plate: string;
  remaining: number;
  total_payment: number;
  items: string[];
  CreatedAt: string;
  DeletedAt: string;
  store_id: string;
}

const Trash: React.FC = () => {
  const [stores, setStores] = useState<Store[]>([]);
  const [deletedVehicles, setDeletedVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedStore, setSelectedStore] = useState<string>("");

  // Fetch stores with deleted vehicles
  useEffect(() => {
    const fetchStores = async () => {
      try {
        setLoading(true);
        const response = await getStoresWithDeletedVehicles(searchTerm);
        if (response && response.data) {
          setStores(response.data);
        }
      } catch (error) {
        console.error("Error fetching stores:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchStores();
  }, [searchTerm]);

  // Fetch deleted vehicles for a specific store (initial load without search)
  const fetchDeletedVehicles = async (storeId: string) => {
    try {
      setLoading(true);
      // Always use empty search term when selecting a store
      const response = await getDeletedVehiclesByStoreId(storeId, 1, "");
      if (response && response.data) {
        setDeletedVehicles(response.data.rows || []);
        setSelectedStore(storeId);
      }
    } catch (error) {
      console.error("Error fetching deleted vehicles:", error);
      setDeletedVehicles([]);
    } finally {
      setLoading(false);
    }
  };

  // Create a debounced search function
  const debouncedSearch = useCallback(
    debounce(async (searchValue: string, storeId: string) => {
      if (storeId) {
        try {
          setLoading(true);
          // Use the searchValue parameter directly instead of the searchTerm state
          const response = await getDeletedVehiclesByStoreId(storeId, 1, searchValue);
          if (response && response.data) {
            setDeletedVehicles(response.data.rows || []);
          }
        } catch (error) {
          console.error("Error fetching deleted vehicles:", error);
          setDeletedVehicles([]);
        } finally {
          setLoading(false);
        }
      }
    }, 300), // Reduced debounce time for better responsiveness
    [setLoading, setDeletedVehicles] // Add dependencies to ensure proper function recreation
  );

  // Reset search when component unmounts or when dependencies change
  useEffect(() => {
    return () => {
      // This cleanup function will run when the component unmounts
      // or when any dependency in the dependency array changes
      debouncedSearch.clear();
    };
  }, [debouncedSearch]);

  // Reset search input when selectedStore changes
  useEffect(() => {
    // Clear search term when store changes
    setSearchTerm("");
    // Clear any pending searches
    debouncedSearch.clear();
  }, [selectedStore, debouncedSearch]);

  // Effect to trigger search when searchTerm changes
  useEffect(() => {
    if (selectedStore && searchTerm !== "") {
      debouncedSearch(searchTerm, selectedStore);
    }
  }, [searchTerm, selectedStore, debouncedSearch]);

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);

    // Only trigger search if we have a selected store
    if (selectedStore) {
      // Cancel any pending debounced searches
      debouncedSearch.clear();
      // Trigger a new search with the updated value
      debouncedSearch(value, selectedStore);
    }
  };

  // Handle restore vehicle
  const handleRestoreVehicle = async (id: string) => {
    try {
      setLoading(true);
      await restoreVehicle(id);
      toast.success("Araç başarıyla geri yüklendi");

      // Refresh the list
      if (selectedStore) {
        // If there's a search term, use the debounced search to refresh with the current search
        if (searchTerm) {
          debouncedSearch(searchTerm, selectedStore);
        } else {
          // Otherwise just fetch all deleted vehicles for the store
          await fetchDeletedVehicles(selectedStore);
        }

        // Refresh the stores list to update counts
        const response = await getStoresWithDeletedVehicles(searchTerm);
        if (response && response.data) {
          setStores(response.data);

          // If the store has no more deleted vehicles, select another store
          const currentStore = response.data.find((store: Store) => store.id === selectedStore);
          if (!currentStore || currentStore.deleted_vehicles_count === 0) {
            const storeWithDeletedVehicles = response.data.find((store: Store) => store.deleted_vehicles_count > 0);
            if (storeWithDeletedVehicles) {
              setSelectedStore(storeWithDeletedVehicles.id);
              fetchDeletedVehicles(storeWithDeletedVehicles.id);
            } else {
              setSelectedStore("");
              setDeletedVehicles([]);
            }
          }
        }
      }
    } catch (error) {
      console.error("Error restoring vehicle:", error);
      toast.error("Araç geri yüklenirken bir hata oluştu");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6 text-gray-800">Silinen Öğeler</h1>

      {/* Search Bar */}
      <div className="relative mb-6">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <FaSearch className="w-5 h-5 text-gray-500" />
        </div>
        <input
          type="text"
          className="block w-full p-4 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500"
          placeholder="Ara..."
          value={searchTerm}
          onChange={handleSearch}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Stores List */}
        <div className="md:col-span-1 bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4 text-gray-700">Silinen Araçları Olan Mağazalar</h2>
          <div className="space-y-2 max-h-[600px] overflow-auto">
            {stores.map((store) => (
              <div
                key={store.id}
                className={`p-3 rounded-lg cursor-pointer transition-all ${
                  selectedStore === store.id
                    ? "bg-blue-100 border-l-4 border-blue-500"
                    : "bg-gray-50 hover:bg-gray-100"
                }`}
                onClick={() => fetchDeletedVehicles(store.id)}
              >
                <h3 className="font-medium">{store.name}</h3>
                <div className="flex justify-between items-center mt-1">
                  <p className="text-sm text-gray-500">{store.phone}</p>
                  <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">
                    {store.deleted_vehicles_count} araç
                  </span>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  <div>Toplam: {Number(store.total_amount).toLocaleString('tr-TR')}₺</div>
                  <div>Kalan: {Number(store.remaining_amount).toLocaleString('tr-TR')}₺</div>
                </div>
              </div>
            ))}
            {stores.length === 0 && !loading && (
              <p className="text-gray-500 text-center py-4">Silinen araçları olan mağaza bulunamadı</p>
            )}
          </div>
        </div>

        {/* Deleted Vehicles */}
        <div className="md:col-span-3 bg-white p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4 text-gray-700">Silinen Araçlar</h2>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[600px] overflow-auto">
              {deletedVehicles.map((vehicle) => (
                <div
                  key={vehicle.id}
                  className="p-4 rounded-lg bg-red-50 border border-red-200 shadow-sm"
                >
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-semibold text-gray-800">{vehicle.person_name}</h3>
                    <div className="text-sm text-gray-600">
                      <div>Kalan: {Number(vehicle.remaining).toLocaleString('tr-TR')}₺</div>
                      <div>Toplam: {Number(vehicle.total_payment).toLocaleString('tr-TR')}₺</div>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">Telefon:</span> {vehicle.phone}
                  </p>
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">Plaka:</span> {vehicle.plate}
                  </p>
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-medium">Parçalar:</span>{" "}
                    {vehicle.items && vehicle.items.length > 0
                      ? vehicle.items.length > 18
                        ? vehicle.items.slice(0, 18).join(", ") + "..."
                        : vehicle.items.join(", ")
                      : "Parça Yok"}
                  </p>
                  <div className="flex justify-between items-center mt-3">
                    <div className="text-xs text-gray-500">
                      Silinme Tarihi: {new Date(vehicle.DeletedAt).toLocaleString()}
                    </div>
                    <button
                      onClick={() => handleRestoreVehicle(vehicle.id)}
                      className="flex items-center gap-1 text-sm bg-green-100 hover:bg-green-200 text-green-800 py-1 px-3 rounded-md transition-colors"
                      disabled={loading}
                    >
                      <FaTrashRestore className="text-xs" />
                      Geri Yükle
                    </button>
                  </div>
                </div>
              ))}

              {deletedVehicles.length === 0 && selectedStore && (
                <div className="col-span-2 text-center py-10">
                  <p className="text-gray-500">Bu mağazada silinen araç bulunamadı</p>
                </div>
              )}

              {!selectedStore && (
                <div className="col-span-2 text-center py-10">
                  <p className="text-gray-500">Silinen araçları görmek için bir mağaza seçin</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Trash;
