import React, { useState } from "react";
import Modal from "../../../component/Modal/Modal";
import { MdOutlineClose } from "react-icons/md";
import { CreateItem } from "../../../types/item";
import { setItem } from "../../../services/itemService";
import { toast } from "react-toastify";

interface IAddItemModal {
  onClose?: () => void;
  isOpen?: boolean;
  setIsOpen?: any;
  onSubmit?: (item: CreateItem) => void;
  fetchItems: any;
  currentPage: any;
}

const AddItemModal: React.FC<IAddItemModal> = ({
  isOpen,
  onClose,
  setIsOpen,
  fetchItems,
  currentPage,
}) => {
  const [form, setForm] = useState<CreateItem>({
    name: "",
    note: "",
    price: "",
    quantity: 0,
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setForm((prev) => ({
      ...prev,
      [name]: name === "quantity" ? parseInt(value) || 0 : value,
    }));
  };

  const createItem = async () => {
    try {
      const response = await setItem(form);
      console.log("response", response);
      if (response) {
        console.log("response", response);
        toast.success("parça başarıyla oluşturuldu.");
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setForm({ name: "", note: "", price: "", quantity: 0 });
      setIsOpen(false);
      fetchItems(currentPage);
    }
  };

  return (
    isOpen && (
      <div className="flex h-full w-full items-center justify-center">
        <Modal
          classname="fixed inset-0 bg-gray-800 bg-opacity-50 p-3 flex items-center justify-center z-40"
          classname2="relative bg-white rounded-3xl w-full md:w-1/3 h-[50%]"
          onClick={onClose}
        >
          <div className="flex flex-col justify-between w-full h-full items-start p-8">
            <div className="w-full">
              <div className="flex flex-row w-full justify-between items-center mb-4">
                <h2 className="text-lg font-bold text-center">Parça Oluştur</h2>
                <MdOutlineClose size={22} onClick={onClose} />
              </div>
            </div>
            <div className="w-full flex flex-col gap-4">
              <input
                type="text"
                name="name"
                value={form.name}
                onChange={handleChange}
                placeholder="İsim"
                className="border rounded p-2 w-full"
              />
              <textarea
                name="note"
                value={form.note}
                onChange={handleChange}
                placeholder="Not"
                className="border rounded p-2 w-full"
              />
              <input
                type="text"
                name="price"
                value={form.price}
                onChange={handleChange}
                placeholder="Fiyat"
                className="border rounded p-2 w-full"
              />
              <input
                type="number"
                name="quantity"
                value={form.quantity > 0 ? form.quantity : ""}
                onChange={handleChange}
                placeholder="Miktar"
                className="border rounded p-2 w-full"
              />
            </div>
            <div className="w-full flex justify-end mt-4 gap-3">
              <button
                onClick={createItem}
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                Oluştur
              </button>
              <button
                onClick={onClose}
                className="bg-red-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                İptal
              </button>
            </div>
          </div>
        </Modal>
      </div>
    )
  );
};

export default AddItemModal;
