// src/pages/Item/Item.tsx

import React, { useEffect, useState } from "react";
import {
  deleteItem,
  getItem,
  setItem,
  updateItem,
} from "../../../services/itemService";
import { CreateItem } from "../../../types/item";
import { FaEdit, FaSave, FaTimes } from "react-icons/fa";
import { MdDeleteForever } from "react-icons/md";
import { toast } from "react-toastify";
import ConfirmModal from "../../../component/Confirm/ConfirmModal";
import { IoAddCircleOutline, IoSearchOutline } from "react-icons/io5";
import Tooltip from "@mui/material/Tooltip";

interface Row {
  id: string;
  CreatedAt: string;
  UpdatedAt: string;
  DeletedAt: string | null;
  updated_by: string;
  updated_date: string | null;
  deleted_by: string;
  deleted_date: string | null;
  maked_by: string;
  name: string;
  price: string;
  quantity: number;
  note: string;
}

interface ItemData {
  page: number;
  per_page: number;
  total: number;
  total_pages: number;
  rows: Row[];
  total_amount?: number; // Make sure to have this optional if not included in original API
}

interface UpdateItem {
  name: string;
  price: string;
  quantity: number;
  note: string;
}

function Item() {
  const [itemData, setItemData] = useState<ItemData | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);

  // State for editing items
  const [editingRowId, setEditingRowId] = useState<string | null>(null);
  const [editFormData, setEditFormData] = useState<UpdateItem>({
    name: "",
    price: "",
    quantity: 0,
    note: "",
  });

  // State for adding a new item
  const [addingItem, setAddingItem] = useState(false);
  const [newItemData, setNewItemData] = useState<CreateItem>({
    name: "",
    price: "",
    quantity: 0,
    note: "",
  });

  // State for delete confirmation
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [deleteItemId, setDeleteItemId] = useState<string>("");
  const [queryKey, setQueryKey] = useState("");
  const [amountData, setAmountData] = useState("");

  // Fetch items from API
  const fetchItems = async (page: number) => {
    try {
      const response = await getItem(page, 10, queryKey);
      setItemData(response.data);
      setAmountData(response.data.total_amount);
    } catch (error) {
      console.error("Error fetching items:", error);
      toast.error("Parçalar alınamadı.");
    }
  };

  // Delete item handler
  const deleteItemHandler = async (id: string) => {
    try {
      await deleteItem(id);
      toast.success("Parça Silindi.");
      fetchItems(currentPage);
    } catch (error) {
      console.error("Error deleting item:", error);
      toast.error("Parça Silinemedi.");
    } finally {
      onCloseConfirm();
    }
  };

  // Create item handler
  const createItemHandler = async () => {
    if (!newItemData.name || !newItemData.price) {
      toast.error("Tüm alanları doldurunuz.");
      return;
    }
    try {
      const response = await setItem(newItemData);
      if (response) {
        toast.success("Parça başarıyla eklendi.");
        setNewItemData({
          name: "",
          price: "",
          quantity: 0,
          note: "",
        });
        setAddingItem(false);
        fetchItems(1); // İlk sayfayı yeniden yükle
      }
    } catch (error) {
      console.error("Error adding item:", error);
      toast.error("Parça eklenemedi.");
    }
  };

  // Update item handler
  const updateItemHandler = async (id: string, data: UpdateItem) => {
    try {
      await updateItem(id, data);
      toast.success("Parça Başarıyla Güncellendi.");
      fetchItems(currentPage);
    } catch (error) {
      console.error("Error updating item:", error);
      toast.error("Parça Güncellenemedi.");
    }
  };

  useEffect(() => {
    fetchItems(currentPage);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, queryKey]);

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= (itemData?.total_pages || 1)) {
      setCurrentPage(page);
    }
  };

  // Handle edit icon click
  const handleEditClick = (row: Row) => {
    setEditingRowId(row.id);
    setEditFormData({
      name: row.name,
      price: row.price,
      quantity: row.quantity,
      note: row.note,
    });
  };

  // Handle form input changes for editing
  const handleEditFormChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setEditFormData({
      ...editFormData,
      [name]: name === "quantity" ? Number(value) : value,
    });
  };

  // Handle save action
  const handleSaveClick = async (id: string) => {
    await updateItemHandler(id, editFormData);
    setEditingRowId(null);
  };

  // Handle cancel action
  const handleCancelClick = () => {
    setEditingRowId(null);
  };

  // Handle new item input changes
  const handleNewItemChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setNewItemData({
      ...newItemData,
      [name]: name === "quantity" ? Number(value) : value,
    });
  };

  // Handle delete confirmation modal
  const onCloseConfirm = () => {
    setConfirmOpen(false);
    setDeleteItemId("");
  };

  if (!itemData || !itemData.rows) {
    return <div className="text-center mt-10">Loading...</div>;
  }

  // Pagination logic for max 5 buttons
  const totalPages = itemData.total_pages || 1;
  const visiblePages = 3;
  let startPage = Math.max(currentPage - Math.floor(visiblePages / 2), 1);
  let endPage = startPage + visiblePages - 1;

  if (endPage > totalPages) {
    endPage = totalPages;
    startPage = Math.max(endPage - visiblePages + 1, 1);
  }

  const pageNumbers = [];
  for (let i = startPage; i <= endPage; i++) {
    pageNumbers.push(i);
  }

  return (
    <div className="container mx-auto p-4">
      {/* Table */}
      <div className="overflow-x-auto">
        <div className="flex items-center border  rounded-lg px-3 py-1 bg-gray-100  focus-within:ring-1 shadow-sm w-full md:w-[40%] m-1">
          <IoSearchOutline className="text-gray-500 mr-2" size={20} />
          <input
            type="text"
            placeholder="Parça Ara..."
            className="flex-1 outline-none bg-transparent text-gray-700 placeholder-gray-400"
            value={queryKey}
            onChange={(e: any) => setQueryKey(e.target.value)}
          />
        </div>
        <table className="table-auto w-full ">
          <thead>
            <tr className="bg-gray-50">
              <th className="py-3 px-4 font-semibold text-gray-700">İSİM</th>
              <th className="py-3 px-4 font-semibold text-gray-700">FİYAT</th>
              <th className="py-3 px-4 font-semibold text-gray-700">MİKTAR</th>
              <th className="py-3 px-4 font-semibold text-gray-700">NOT</th>
              <th className="py-3 px-4 font-semibold text-gray-700">
                OLUŞTURULMA TARİHİ
              </th>
              <th className="py-3 px-4 font-semibold text-gray-700">
                AKSİYONLAR
              </th>
            </tr>
          </thead>
          <tbody>
            {itemData.rows.map((row, index) => (
              <tr
                key={row.id}
                className={`${
                  index % 2 === 0 ? "bg-gray-100" : "bg-white"
                } hover:bg-gray-200`}
              >
                {editingRowId === row.id ? (
                  // Editable Row
                  <>
                    <td className="py-4 px-6 text-sm text-center">
                      <input
                        type="text"
                        name="name"
                        value={editFormData.name}
                        onChange={handleEditFormChange}
                        className="border rounded px-2 py-1 w-full"
                      />
                    </td>
                    <td className="py-4 px-6 text-sm text-center">
                      <input
                        type="text"
                        name="price"
                        value={editFormData.price}
                        onChange={handleEditFormChange}
                        className="border rounded px-2 py-1 w-full"
                      />
                    </td>
                    <td className="py-4 px-6 text-sm text-center">
                      <input
                        type="number"
                        name="quantity"
                        value={editFormData.quantity}
                        onChange={handleEditFormChange}
                        className="border rounded px-2 py-1 w-full"
                        min="1"
                      />
                    </td>
                    <td className="py-4 px-6 text-sm text-center">
                      <textarea
                        name="note"
                        value={editFormData.note}
                        onChange={handleEditFormChange}
                        className="border rounded px-2 py-1 w-full"
                      />
                    </td>
                    <td className="py-4 px-6 text-sm text-center">
                      {new Date(row.CreatedAt).toLocaleString()}
                    </td>
                    <td className="py-4 px-6 text-sm text-center">
                      <div className="flex justify-center items-center gap-2">
                        <button
                          onClick={() => handleSaveClick(row.id)}
                          className="flex items-center gap-1 text-green-500 hover:text-green-700"
                        >
                          <FaSave /> Kaydet
                        </button>
                        <button
                          onClick={handleCancelClick}
                          className="flex items-center gap-1 text-red-500 hover:text-red-700"
                        >
                          <FaTimes /> İptal
                        </button>
                      </div>
                    </td>
                  </>
                ) : (
                  // Read-Only Row
                  <>
                    <Tooltip title={row.name}>
                      <td className="py-2 px-4 text-sm text-center  cursor-pointer">
                        {row.name.length > 20
                          ? row.name.slice(0, 10) + "..."
                          : row.name}
                      </td>
                    </Tooltip>
                    <td className="py-4 px-6 text-sm text-center">
                      {row.price} ₺
                    </td>
                    <td className="py-4 px-6 text-sm text-center">
                      {row.quantity}
                    </td>
                    <Tooltip title={row.note}>
                      <td className="py-2 px-4 text-sm text-center  cursor-pointer">
                        {row.note.length > 20
                          ? row.note.slice(0, 10) + "..."
                          : row.note}
                      </td>
                    </Tooltip>
                    <td className="py-4 px-6 text-sm text-center">
                      {new Date(row.CreatedAt).toLocaleString()}
                    </td>
                    <td className="py-4 px-6 text-sm text-center">
                      <div className="flex justify-center items-center gap-3">
                        <MdDeleteForever
                          onClick={() => {
                            setConfirmOpen(true);
                            setDeleteItemId(row.id);
                          }}
                          className="hover:opacity-60 cursor-pointer"
                          size={22}
                          color="red"
                        />
                        <FaEdit
                          onClick={() => handleEditClick(row)}
                          className="hover:opacity-60 cursor-pointer"
                          size={22}
                        />
                      </div>
                    </td>
                  </>
                )}
              </tr>
            ))}

            {/* Yeni Parça Ekleme Satırı */}
            {addingItem && (
              <tr className="bg-gray-100">
                <td className="py-4 px-6 text-sm text-center">
                  <input
                    type="text"
                    name="name"
                    value={newItemData.name}
                    onChange={handleNewItemChange}
                    className="border rounded px-2 py-1 w-full"
                    placeholder="İsim"
                  />
                </td>
                <td className="py-4 px-6 text-sm text-center">
                  <input
                    type="text"
                    name="price"
                    value={newItemData.price}
                    onChange={handleNewItemChange}
                    className="border rounded px-2 py-1 w-full"
                    placeholder="Fiyat"
                  />
                </td>
                <td className="py-4 px-6 text-sm text-center">
                  <input
                    type="number"
                    name="quantity"
                    value={newItemData.quantity > 0 ? newItemData.quantity : ""}
                    onChange={handleNewItemChange}
                    className="border rounded px-2 py-1 w-full"
                    placeholder="Miktar"
                    min="1"
                  />
                </td>
                <td className="py-4 px-6 text-sm text-center">
                  <textarea
                    name="note"
                    value={newItemData.note}
                    onChange={handleNewItemChange}
                    className="border rounded px-2 py-1 w-full"
                    placeholder="Not"
                  />
                </td>
                <td className="py-4 px-6 text-sm text-center">
                  {/* Oluşturulma Tarihi otomatik olarak eklenir */}
                  --
                </td>
                <td className="py-4 px-6 text-sm text-center">
                  <div className="flex justify-center items-center gap-2">
                    <button
                      onClick={createItemHandler}
                      className="flex items-center gap-1 text-green-500 hover:text-green-700"
                    >
                      <FaSave /> Kaydet
                    </button>
                    <button
                      onClick={() => {
                        setAddingItem(false);
                        setNewItemData({
                          name: "",
                          price: "",
                          quantity: 0,
                          note: "",
                        });
                      }}
                      className="flex items-center gap-1 text-red-500 hover:text-red-700"
                    >
                      <FaTimes /> İptal
                    </button>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
          {!addingItem && (
            <tfoot>
              <tr className="bg-gray-100 font-bold">
                <td colSpan={6} className="py-2 px-4">
                  <div className="w-full flex justify-center">
                    <div className="w-[50%] flex justify-center">
                      {" "}
                      <button
                        onClick={() => setAddingItem(true)}
                        className="flex items-center  justify-center gap-2 bg-blue-500 text-white  rounded-2xl hover:bg-blue-500 w-[80%]"
                      >
                        <IoAddCircleOutline size={22} /> Parça Ekle
                      </button>
                    </div>

                    <div className="w-[50%] flex justify-center">
                      <div className="bg-green-100 border border-green-500 text-green-500 p-2 flex justify-center items-center w-1/2">
                        Toplam: {Number(amountData).toLocaleString("TR-tr")}₺
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            </tfoot>
          )}
        </table>
      </div>

      {/* Pagination Controls */}
      <div className="flex justify-center items-center mt-6 space-x-2">
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className={`px-4 py-2 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 ${
            currentPage === 1 ? "cursor-not-allowed opacity-50" : ""
          }`}
        >
          Prev
        </button>

        {pageNumbers[0] > 1 && (
          <>
            <button
              onClick={() => handlePageChange(1)}
              className={`px-4 py-2 rounded bg-gray-200 text-gray-700 hover:bg-gray-300`}
            >
              1
            </button>
            {pageNumbers[0] > 2 && <span>...</span>}
          </>
        )}

        {pageNumbers.map((page) => (
          <button
            key={page}
            onClick={() => handlePageChange(page)}
            className={`px-4 py-2 rounded ${
              currentPage === page
                ? "bg-indigo-600 text-white"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
            }`}
          >
            {page}
          </button>
        ))}

        {pageNumbers[pageNumbers.length - 1] < totalPages && (
          <>
            {pageNumbers[pageNumbers.length - 1] < totalPages - 1 && (
              <span>...</span>
            )}
            <button
              onClick={() => handlePageChange(totalPages)}
              className={`px-4 py-2 rounded bg-gray-200 text-gray-700 hover:bg-gray-300`}
            >
              {totalPages}
            </button>
          </>
        )}

        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className={`px-4 py-2 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 ${
            currentPage === totalPages ? "cursor-not-allowed opacity-50" : ""
          }`}
        >
          Next
        </button>
      </div>

      {/* Confirm Deletion Modal */}
      <ConfirmModal
        confirmOpen={confirmOpen}
        onClose={onCloseConfirm}
        dataID={deleteItemId}
        handler={deleteItemHandler}
      />
    </div>
  );
}

export default Item;
