import { useEffect, useState } from "react";
import {
  deleteStore,
  getStore,
  setStore,
  updateStore,
} from "../../../services/storeService";
import { useNavigate } from "react-router-dom";
import { MdDeleteForever } from "react-icons/md";
import { FaArrowRightFromBracket } from "react-icons/fa6";
import { FaEdit } from "react-icons/fa";
import { toast } from "react-toastify";
import ConfirmModal from "../../../component/Confirm/ConfirmModal";
import { IoAddCircleOutline } from "react-icons/io5";
import { IoSearchOutline } from "react-icons/io5";
import { MdContentCopy } from "react-icons/md";
import Tooltip from "@mui/material/Tooltip";
import { Button, Input } from "@mui/material";

interface Row {
  id: string;
  CreatedAt: string;
  UpdatedAt: string;
  DeletedAt: string | null;
  deleted_by: string;
  deleted_date: string | null;
  maked_by: string;
  name: string;
  note: string;
  person_name: string;
  phone_number: string;
  updated_by: string;
  updated_date: string | null;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  slug_name: string;
}

interface StoreData {
  page: number;
  per_page: number;
  total: number;
  total_pages: number;
  rows: Row[];
}

export interface CreateStore {
  name: string;
  phone_number: string;
  person_name: string;
  note: string;
}

function Store() {
  const [storeData, setStoreData] = useState<StoreData | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [rowID, setRowID] = useState<string>("");

  const navigate = useNavigate();

  const [addingStore, setAddingStore] = useState<boolean>(false);
  const [newStoreData, setNewStoreData] = useState<CreateStore>({
    name: "",
    phone_number: "",
    person_name: "",
    note: "",
  });

  const [editingRowId, setEditingRowId] = useState<string | null>(null);
  const [editFormData, setEditFormData] = useState<CreateStore>({
    name: "",
    phone_number: "",
    person_name: "",
    note: "",
  });
  const [queryKey, setQueryKey] = useState("");

  const onCloseConfirm = () => {
    setConfirmOpen(false);
  };

  const getStoreData = async (page: number) => {
    try {
      const response = await getStore(page, 10, queryKey);
      setStoreData(response.data);
    } catch (error) {
      console.error("Failed to fetch store data:", error);
    }
  };

  const deleteStoreData = async (id: string) => {
    try {
      const response = await deleteStore(id);
      if (response) {
        toast.success("Başarıyla Silindi.");
        getStoreData(currentPage);
      }
    } catch (error) {
      console.error("Failed to delete store data:", error);
      toast.error("Silinemedi.");
    } finally {
      setConfirmOpen(false);
    }
  };

  useEffect(() => {
    getStoreData(currentPage);
  }, [currentPage, queryKey]);

  const handlePageChange = (page: number) => {
    if (storeData && page >= 1 && page <= storeData.total_pages) {
      setCurrentPage(page);
    }
  };

  const handleNextPage = () => {
    if (storeData && currentPage < storeData.total_pages) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  // Yeni müşteri ekleme işlemleri
  const handleAddStore = () => {
    setAddingStore(true);
  };

  const handleNewStoreChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setNewStoreData({
      ...newStoreData,
      [name]: value,
    });
  };

  const handleSaveNewStore = async () => {
    try {
      const response = await setStore(newStoreData);
      if (response) {
        toast.success("Müşteri başarıyla eklendi.");
        setNewStoreData({
          name: "",
          phone_number: "",
          person_name: "",
          note: "",
        });
        setAddingStore(false);
        getStoreData(1);
      }
    } catch (error) {
      console.error("Failed to add store:", error);
      toast.error("Müşteri eklenemedi.");
    }
  };

  const handleCancelNewStore = () => {
    setAddingStore(false);
    setNewStoreData({
      name: "",
      phone_number: "",
      person_name: "",
      note: "",
    });
  };

  // Düzenleme işlemleri
  const handleEditClick = (row: Row) => {
    setEditingRowId(row.id);
    setEditFormData({
      name: row.name,
      phone_number: row.phone_number,
      person_name: row.person_name,
      note: row.note,
    });
  };

  const handleEditFormChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setEditFormData({
      ...editFormData,
      [name]: value,
    });
  };

  const handleSaveEdit = async (id: string) => {
    try {
      await updateStore(id, editFormData);
      toast.success("Müşteri başarıyla güncellendi.");
      setEditingRowId(null);
      getStoreData(currentPage);
    } catch (error) {
      console.error("Failed to update store data:", error);
      toast.error("Güncellenemedi.");
    }
  };

  const handleCancelEdit = () => {
    setEditingRowId(null);
  };

  const copyToClipboard = (slugName: string) => {
    navigator.clipboard
      .writeText(slugName)
      .then(() => {
        toast.success("Link Kopyalandı! 🚀🚀🚀");
      })
      .catch((err) => {
        console.error("Failed to copy slug name:", err);
      });
  };

  // Sayfalama (Item.tsx'deki mantığa benzer)
  let pageNumbers: number[] = [];
  if (storeData) {
    const totalPages = storeData.total_pages || 1;

    if (totalPages <= 5) {
      // 5 veya daha az sayfa varsa hepsini göster
      pageNumbers = Array.from({ length: totalPages }, (_, i) => i + 1);
    } else {
      // Daha fazla sayfa varsa, sadece 1, ortadaki sayfa, ... , son sayfa şeklinde göster
      const visiblePages = 3; // Ortada görünmesini istediğimiz sayfa sayısı
      let startPage = Math.max(currentPage - Math.floor(visiblePages / 2), 1);
      let endPage = startPage + visiblePages - 1;

      if (endPage > totalPages) {
        endPage = totalPages;
        startPage = Math.max(endPage - visiblePages + 1, 1);
      }

      pageNumbers = [];
      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
      }
    }
  }

  return (
    <div className="w-full mx-auto p-4">
      <div className="w-full flex flex-col md:flex-row justify-between items-center">
        <h1 className="text-2xl md:text-3xl font-bold mb-4 md:mb-6 text-center text-gray-800">
          Müşteriler
        </h1>
      </div>
      <div className="w-full overflow-x-auto border rounded-xl">
        <div className="w-full p-1">
          <div className="flex items-center border  rounded-lg px-3 py-1 bg-gray-100  focus-within:ring-1 shadow-sm w-full md:w-[40%] ">
            <IoSearchOutline className="text-gray-500 mr-2" size={20} />
            <input
              type="text"
              placeholder="Müşteri Ara..."
              className="flex-1 outline-none bg-transparent text-gray-700 placeholder-gray-400"
              value={queryKey}
              onChange={(e: any) => setQueryKey(e.target.value)}
            />
          </div>
        </div>
        <table className="table-auto w-full ">
          <thead>
            <tr className="bg-gray-50 w-full">
              <th className="py-3 px-2 md:px-4 font-semibold text-gray-700">
                İSİM
              </th>
              <th className="py-3 px-2 md:px-4 font-semibold text-gray-700">
                TELEFON NUMARASI
              </th>
              <th className="py-3 px-2 md:px-4 font-semibold text-gray-700">
                YETKİLİ ADI
              </th>
              <th className="py-3 px-2 md:px-4 font-semibold text-gray-700 ">
                NOT
              </th>
              <th className="py-3 px-2 md:px-4 font-semibold text-gray-700">
                OLUŞTURULMA TARİHİ
              </th>
              <th className="py-3 px-2 md:px-4 font-semibold text-gray-700">
                KALAN TUTAR
              </th>
              <th className="py-3 px-2 md:px-4 font-semibold text-gray-700">
                TOPLAM TUTAR
              </th>
              <th className="py-3 px-2 md:px-4 font-semibold text-gray-700">
                AKSİYONLAR
              </th>
            </tr>
          </thead>
          <tbody>
            {storeData?.rows ? (
              storeData.rows.map((row) => (
                <tr key={row.id} className={`hover:bg-gray-200 border`}>
                  {editingRowId === row.id ? (
                    // Düzenleme satırı
                    <>
                      <td className="py-2 px-4 text-sm text-center">
                        <Input
                          type="text"
                          name="name"
                          value={editFormData.name}
                          onChange={handleEditFormChange}
                          className="border rounded px-2 py-1 w-full"
                        />
                      </td>
                      <td className="py-2 px-4 text-sm text-center">
                        <Input
                          type="text"
                          name="phone_number"
                          value={editFormData.phone_number}
                          onChange={handleEditFormChange}
                          className="border rounded px-2 py-1 w-full"
                        />
                      </td>
                      <td className="py-2 px-4 text-sm text-center">
                        <Input
                          type="text"
                          name="person_name"
                          value={editFormData.person_name}
                          onChange={handleEditFormChange}
                          className="border rounded px-2 py-1 w-full"
                        />
                      </td>
                      <td className="py-2 px-4 text-sm text-center">
                        <Input
                          name="note"
                          value={editFormData.note}
                          onChange={handleEditFormChange}
                          className="border rounded px-2 py-1 w-full"
                        />
                      </td>
                      <td className="py-2 px-4 text-sm text-center">
                        {new Date(row.CreatedAt).toLocaleString()}
                      </td>
                      <td className="py-2 px-4 text-sm text-center flex justify-center items-center gap-2 md:gap-5">
                        <Button
                          color="primary"
                          onClick={() => handleSaveEdit(row.id)}
                          className="text-green-500 hover:text-green-700"
                        >
                          Kaydet
                        </Button>
                        <Button
                          color="error"
                          onClick={handleCancelEdit}
                          className="text-red-500 hover:text-red-700"
                        >
                          İptal
                        </Button>
                      </td>
                    </>
                  ) : (
                    // Normal görüntüleme satırı
                    <>
                      <td className="py-2 px-4 text-sm text-center">
                        {row.name.split(" ").length > 5
                          ? row.name.split(" ").slice(0, 3).join(" ") + "..."
                          : row.name}
                      </td>
                      <td className="py-2 px-4 text-sm text-center">
                        {row.phone_number.split(" ").length > 5
                          ? row.phone_number.split(" ").slice(0, 3).join(" ") +
                            "..."
                          : row.phone_number}
                      </td>
                      <td className="py-2 px-4 text-sm text-center">
                        {row.person_name.split(" ").length > 5
                          ? row.person_name.split(" ").slice(0, 3).join(" ") +
                            "..."
                          : row.person_name}
                      </td>
                      <Tooltip title={row.note}>
                        <td className="py-2 px-4 text-sm text-center cursor-pointer">
                          {row.note.split(" ").length > 5
                            ? row.note.split(" ").slice(0, 3).join(" ") + "..."
                            : row.note}
                        </td>
                      </Tooltip>
                      <td className="py-2 px-4 text-sm text-center">
                        {new Date(row.CreatedAt).toLocaleString()}
                      </td>{" "}
                      <td className="py-2 px-4 text-sm text-center font-bold">
                        {row.remaining_amount}₺
                      </td>
                      <td className="py-2 px-4 text-sm text-center font-bold">
                        {row.total_amount}₺
                      </td>
                      <td className="py-2 px-4 text-sm text-center flex justify-center items-center gap-2 md:gap-5">
                        <button
                          className="text-blue-600 p-2 flex justify-center items-center gap-2 border bg-blue-100 rounded-2xl"
                          onClick={() => navigate(`/store/${row.id}`)}
                        >
                          <span className="hidden md:block">
                            Detayları Göster
                          </span>
                          <FaArrowRightFromBracket />
                        </button>
                        <MdDeleteForever
                          className="hover:opacity-60 cursor-pointer"
                          size={22}
                          color="red"
                          onClick={() => {
                            setConfirmOpen(true);
                            setRowID(row.id);
                          }}
                        />
                        <FaEdit
                          onClick={() => handleEditClick(row)}
                          className="hover:opacity-60 cursor-pointer"
                          size={20}
                        />
                        <MdContentCopy
                          onClick={() =>
                            copyToClipboard(
                              `https://app.kalkanoto.com.tr/stores/${row.slug_name}`
                            )
                          }
                          className="hover:opacity-60 cursor-pointer text-green-600"
                          size={22}
                        />
                      </td>
                    </>
                  )}
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className="py-6 text-center text-gray-500">
                  Veri Bulunamadı!
                </td>
              </tr>
            )}
            {addingStore && (
              <tr className="border bg-gray-100">
                {/* Yeni müşteri ekleme satırı */}
                <td className="py-2 px-4 text-sm text-center">
                  <Input
                    type="text"
                    name="name"
                    value={newStoreData.name}
                    onChange={handleNewStoreChange}
                    className="border rounded px-2 py-1 w-full"
                    placeholder="İsim"
                  />
                </td>
                <td className="py-2 px-4 text-sm text-center">
                  <Input
                    type="text"
                    name="phone_number"
                    value={newStoreData.phone_number}
                    onChange={handleNewStoreChange}
                    className="border rounded px-2 py-1 w-full"
                    placeholder="Telefon Numarası"
                  />
                </td>
                <td className="py-2 px-4 text-sm text-center">
                  <Input
                    type="text"
                    name="person_name"
                    value={newStoreData.person_name}
                    onChange={handleNewStoreChange}
                    className="border rounded px-2 py-1 w-full"
                    placeholder="Yetkili Adı"
                  />
                </td>
                <td className="py-2 px-4 text-sm text-center">
                  <Input
                    name="note"
                    value={newStoreData.note}
                    onChange={handleNewStoreChange}
                    className="border rounded px-2 py-1 w-full"
                    placeholder="Not"
                  />
                </td>
                <td className="py-2 px-4 text-sm text-center">--</td>
                <td className="py-2 px-4 text-sm text-center flex justify-center items-center gap-2 md:gap-5">
                  <Button
                    color="primary"
                    onClick={handleSaveNewStore}
                    className="text-green-500 hover:text-green-700"
                  >
                    Kaydet
                  </Button>
                  <Button
                    color="error"
                    onClick={handleCancelNewStore}
                    className="text-red-500 hover:text-red-700"
                  >
                    İptal
                  </Button>
                </td>
              </tr>
            )}
          </tbody>
          {!addingStore && (
            <tfoot>
              <tr>
                <td colSpan={8}>
                  <div className="flex justify-center mt-4 mb-2">
                    <button
                      onClick={handleAddStore}
                      className="bg-blue-500 text-white rounded-lg px-4 py-1 flex items-center justify-center gap-2"
                    >
                      <IoAddCircleOutline /> Müşteri Ekle
                    </button>
                  </div>
                </td>
              </tr>
            </tfoot>
          )}
        </table>
      </div>

      {/* Sayfalama Kontrolleri */}
      {storeData && (
        <div className="flex flex-wrap justify-center items-center mt-6 space-x-2">
          <button
            onClick={handlePreviousPage}
            disabled={currentPage === 1}
            className={`px-4 py-2 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 ${
              currentPage === 1 ? "cursor-not-allowed opacity-50" : ""
            }`}
          >
            Prev
          </button>

          {storeData.total_pages <= 5 ? (
            // 5 veya daha az sayfa var ise hepsini göster
            pageNumbers.map((page) => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-4 py-2 rounded ${
                  currentPage === page
                    ? "bg-black text-white"
                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
              >
                {page}
              </button>
            ))
          ) : (
            <>
              {/* 1. sayfa */}
              <button
                onClick={() => handlePageChange(1)}
                className={`px-4 py-2 rounded ${
                  currentPage === 1
                    ? "bg-black text-white"
                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
              >
                1
              </button>

              {/* Eğer gösterilen ilk sayfa 2'den büyükse ... göster */}
              {pageNumbers[0] > 2 && <span className="px-2">...</span>}

              {/* Orta kısımdaki sayfalar */}
              {pageNumbers
                .filter((p) => p !== 1 && p !== storeData.total_pages)
                .map((page) => (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`px-4 py-2 rounded ${
                      currentPage === page
                        ? "bg-black text-white"
                        : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                    }`}
                  >
                    {page}
                  </button>
                ))}

              {/* Eğer gösterilen son sayfa total_pages'ten küçükse ... göster */}
              {pageNumbers[pageNumbers.length - 1] < storeData.total_pages && (
                <span className="px-2">...</span>
              )}

              {/* Son sayfa */}
              <button
                onClick={() => handlePageChange(storeData.total_pages)}
                className={`px-4 py-2 rounded ${
                  currentPage === storeData.total_pages
                    ? "bg-black text-white"
                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
              >
                {storeData.total_pages}
              </button>
            </>
          )}

          <button
            onClick={handleNextPage}
            disabled={currentPage === storeData.total_pages}
            className={`px-4 py-2 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 ${
              currentPage === storeData.total_pages
                ? "cursor-not-allowed opacity-50"
                : ""
            }`}
          >
            Next
          </button>
        </div>
      )}

      <ConfirmModal
        confirmOpen={confirmOpen}
        onClose={onCloseConfirm}
        dataID={rowID}
        handler={deleteStoreData}
      />
    </div>
  );
}

export default Store;
