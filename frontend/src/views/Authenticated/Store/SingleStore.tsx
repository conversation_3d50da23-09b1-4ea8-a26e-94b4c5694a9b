import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { getSingleStore } from "../../../services/storeService";
import {
  deleteVechile,
  getStoreVechile,
  updateVechileService,
  setVechile, // Araç ekleme işlemi için gerekli fonksiyon
} from "../../../services/vechile";
import { IVechile, UpdateVehicle } from "../../../types/vechile";
import { MdDeleteForever } from "react-icons/md";
import { FaEdit, FaPlus, FaSave, FaTimes } from "react-icons/fa";
import { IoIosArrowBack } from "react-icons/io";
import { toast } from "react-toastify";
import ConfirmModal from "../../../component/Confirm/ConfirmModal";
import { IoSearchOutline } from "react-icons/io5";
import { Tooltip } from "@mui/material";
import { FaArrowRightFromBracket } from "react-icons/fa6";

function SingleStore() {
  const [data, setData] = useState({
    name: "",
    note: "",
    person_name: "",
    phone_number: "",
  });
  const [vechiles, setVechiles] = useState<IVechile[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false);
  const { id } = useParams<{ id?: string }>();

  const [confirmOpen, setConfirmOpen] = useState(false);
  const [rowID, setRowID] = useState<string>("");

  const navigate = useNavigate();

  const onCloseConfirm = () => {
    setConfirmOpen(false);
  };

  // State for editing vehicles
  const [editingRowId, setEditingRowId] = useState<string | null>(null);
  const [editFormData, setEditFormData] = useState<UpdateVehicle>({
    plate_number: "",
    phone: "",
    person_name: "",
  });

  // State for adding a new vehicle
  const [addingVehicle, setAddingVehicle] = useState(false);
  const [newVehicleData, setNewVehicleData] = useState({
    person_name: "",
    phone: "",
    plate_number: "",
    items: "", // Parçalar virgülle ayrılmış string
  });
  const [amountData, setAmountData] = useState({
    total: "",
    rem: "",
    paid: "",
  });
  const [queryKey, setQueryKey] = useState("");

  const getData = async () => {
    if (!id) {
      return;
    }
    try {
      const response = await getSingleStore(id);
      if (response) {
        setData(response.data);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Mağaza verileri alınamadı.");
    }
  };

  const getVechileDatas = async (page: number) => {
    if (!id) {
      return;
    }
    try {
      setLoading(true);
      const response = await getStoreVechile(id, page, 10, queryKey);
      setVechiles(response.data.rows || []);
      setTotalPages(response.data.total_pages || 1);
      setAmountData({
        total: response.data.total_amount,
        paid: response.data.paid_amount,
        rem: response.data.remaining_amount,
      });
    } catch (error) {
      console.error("Error fetching vehicles:", error);
      toast.error("Araçlar alınamadı.");
    } finally {
      setLoading(false);
    }
  };

  const updateVechileHandler = async (
    vechileId: string,
    data: UpdateVehicle
  ) => {
    try {
      const response = await updateVechileService(vechileId, data);
      if (response) {
        toast.success("Araç başarıyla güncellendi.");
        getVechileDatas(currentPage);
      }
    } catch (error) {
      toast.error("Araç güncellenemedi.");
    }
  };

  const deleteVechileHandler = async (vechileId: string) => {
    try {
      const response = await deleteVechile(vechileId);
      if (response) {
        toast.success("Araç başarıyla silindi.");
        getVechileDatas(currentPage);
      }
    } catch (error) {
      toast.error("Araç silinemedi.");
    } finally {
      setConfirmOpen(false);
    }
  };

  const createVechileHandler = async () => {
    if (!id) {
      toast.error("Mağaza ID'si bulunamadı.");
      return;
    }

    try {
      const newVechile = {
        store_id: id,
        person_name: newVehicleData.person_name,
        phone: newVehicleData.phone,
        plate_number: newVehicleData.plate_number,
        items: newVehicleData.items
          .split(",")
          .map((item: string) => item.trim())
          .filter((item: string) => item !== ""),
      };
      const response = await setVechile(newVechile);
      if (response) {
        toast.success("Araç başarıyla eklendi.");
        setNewVehicleData({
          person_name: "",
          phone: "",
          plate_number: "",
          items: "",
        });
        setAddingVehicle(false);
        getVechileDatas(1); // İlk sayfayı yeniden yükle
      }
    } catch (error) {
      console.error("Error adding vehicle:", error);
      toast.error("Araç eklenemedi.");
    }
  };

  useEffect(() => {
    getData();
    getVechileDatas(currentPage);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, queryKey]);

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  // Handle edit icon click
  const handleEditClick = (rowData: IVechile) => {
    setEditingRowId(rowData.id);
    setEditFormData({
      plate_number: rowData.plate_number,
      phone: rowData.phone,
      person_name: rowData.person_name,
    });
  };

  // Handle form input changes for editing
  const handleEditFormChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setEditFormData({
      ...editFormData,
      [name]: value,
    });
  };

  // Handle save action for editing
  const handleSaveClick = async (vechileId: string) => {
    await updateVechileHandler(vechileId, editFormData);
    setEditingRowId(null);
  };

  // Handle cancel action for editing
  const handleCancelClick = () => {
    setEditingRowId(null);
  };

  // Handle new vehicle input changes
  const handleNewVehicleChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setNewVehicleData({
      ...newVehicleData,
      [name]: value,
    });
  };

  // Pagination logic (benzer mantık Item.tsx den)
  let pageNumbers: number[] = [];
  if (totalPages <= 5) {
    // 5 veya daha az sayfa varsa hepsini göster
    pageNumbers = Array.from({ length: totalPages }, (_, i) => i + 1);
  } else {
    // Daha fazla sayfa varsa
    const visiblePages = 3;
    let startPage = Math.max(currentPage - Math.floor(visiblePages / 2), 1);
    let endPage = startPage + visiblePages - 1;

    if (endPage > totalPages) {
      endPage = totalPages;
      startPage = Math.max(endPage - visiblePages + 1, 1);
    }

    pageNumbers = [];
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }
  }

  return (
    <div className="container mx-auto p-4">
      <div className="w-full p-1"></div>
      <div className="flex items-center justify-between mb-6">
        <button
          onClick={() => window.history.back()}
          className="flex items-center gap-2 text-gray-800 font-medium px-4 py-2 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-400"
        >
          <IoIosArrowBack /> Geri Dön
        </button>
        <div className="text-xl font-semibold text-gray-600">Araç Tablosu</div>
        <h1 className="text-2xl font-bold text-gray-800">{data.name}</h1>
      </div>

      {/* Table */}
      <div className="overflow-x-auto border rounded-xl ">
        <div className="flex items-center border  rounded-lg px-3 py-1 bg-gray-100  focus-within:ring-1 shadow-sm w-full md:w-[40%] m-1">
          <IoSearchOutline className="text-gray-500 mr-2" size={20} />
          <input
            type="text"
            placeholder="Araç Ara..."
            className="flex-1 outline-none bg-transparent text-gray-700 placeholder-gray-400"
            value={queryKey}
            onChange={(e: any) => setQueryKey(e.target.value)}
          />
        </div>
        <table className="min-w-full table-auto ">
          <thead>
            <tr className="bg-gray-50">
              <th className="py-3 px-4 font-semibold text-gray-700 text-left w-40">
                İSİM
              </th>
              <th className="py-3 px-4 font-semibold text-gray-700 text-center w-24">
                PARÇALAR
              </th>
              <th className="py-3 px-4 font-semibold text-gray-700 text-center w-24">
                TELEFON NUMARASI
              </th>
              <th className="py-3 px-4 font-semibold text-gray-700 text-center w-24">
                PLAKA
              </th>
              <th className="py-3 px-4 font-semibold text-gray-700 text-center w-32">
                OLUŞTURULMA TARİHİ
              </th>
              <th className="py-3 px-4 font-semibold text-gray-700 text-center w-24">
                KALAN TUTAR
              </th>
              <th className="py-3 px-4 font-semibold text-gray-700 text-center w-24">
                TOPLAM TUTAR
              </th>
              <th className="py-3 px-4 font-semibold text-gray-700 text-center w-24">
                AKSİYONLAR
              </th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={8} className="py-6 text-center text-gray-500">
                  Loading...
                </td>
              </tr>
            ) : vechiles.length > 0 ? (
              vechiles.map((row) => (
                <tr
                  key={row.id}
                  className={`hover:opacity-70  ${
                    Number(row.remaining) === 0 ||
                    Number(row.total_payment) === 0
                      ? "bg-green-200"
                      : "bg-red-200"
                  } border-b last:border-0`}
                >
                  {editingRowId === row.id ? (
                    // Editable Row
                    <>
                      <td className="py-2 px-4 text-sm">
                        <input
                          type="text"
                          name="person_name"
                          value={editFormData.person_name}
                          onChange={handleEditFormChange}
                          className="border rounded px-2 py-1 w-full"
                        />
                      </td>
                      <td className="py-2 px-4 text-sm text-center w-24">
                        <Tooltip title={row.items}>
                          <div className="w-full overflow-hidden text-ellipsis whitespace-nowrap">
                            {row.items && row.items.length > 2
                              ? `${row.items.slice(0, 2).join(", ")}...`
                              : row.items?.join(", ")}
                          </div>
                        </Tooltip>
                      </td>
                      <td className="py-2 px-4 text-sm text-center">
                        <input
                          type="text"
                          name="phone"
                          value={editFormData.phone}
                          onChange={handleEditFormChange}
                          className="border rounded px-2 py-1 w-full text-center"
                        />
                      </td>
                      <td className="py-2 px-4 text-sm text-center">
                        <input
                          type="text"
                          name="plate_number"
                          value={editFormData.plate_number}
                          onChange={handleEditFormChange}
                          className="border rounded px-2 py-1 w-full text-center"
                        />
                      </td>
                      <td className="py-2 px-4 text-sm text-center">
                        {new Date(row.CreatedAt).toLocaleString()}
                      </td>
                      <td className="py-2 px-4 text-sm text-center font-bold">
                        {row.remaining.toFixed(2)} ₺
                      </td>
                      <td className="py-2 px-4 text-sm text-center font-bold">
                        {row.total_payment.toFixed(2)} ₺
                      </td>
                      <td className="py-2 px-4 text-sm text-center flex justify-center items-center gap-3">
                        <button
                          onClick={() => handleSaveClick(row.id)}
                          className="flex items-center gap-1 text-green-500 hover:text-green-700"
                        >
                          <FaSave /> Kaydet
                        </button>
                        <button
                          onClick={handleCancelClick}
                          className="flex items-center gap-1 text-red-500 hover:text-red-700"
                        >
                          <FaTimes /> İptal
                        </button>
                      </td>
                    </>
                  ) : (
                    // Read-Only Row
                    <>
                      <td className="py-2 px-4 text-sm text-left w-40">
                        {row.person_name}
                      </td>

                      <Tooltip title={row.items}>
                        <td className="py-2 px-4 text-sm text-center w-24">
                          {" "}
                          <div className="w-full overflow-hidden text-ellipsis whitespace-nowrap">
                            {row.items && row.items.length > 2
                              ? `${row.items.slice(0, 2).join(", ")}...`
                              : row.items?.join(", ")}
                          </div>
                        </td>
                      </Tooltip>

                      <td className="py-2 px-4 text-sm text-center w-24">
                        {row.phone}
                      </td>
                      <td className="py-2 px-4 text-sm text-center w-24">
                        {row.plate_number}
                      </td>
                      <td className="py-2 px-4 text-sm text-center w-32">
                        {new Date(row.CreatedAt).toLocaleString()}
                      </td>
                      <td className="py-2 px-4 text-sm text-center font-bold w-24">
                        {row.remaining.toFixed(2)} ₺
                      </td>
                      <td className="py-2 px-4 text-sm text-center font-bold w-24">
                        {row.total_payment.toFixed(2)} ₺
                      </td>
                      <td className="py-2 px-6 text-sm text-center flex justify-center items-center gap-3">
                        <button
                          onClick={() => navigate(`/vechile/${row.id}`)}
                          className="text-blue-600 p-2 border bg-blue-100 rounded-lg flex items-center gap-2 hover:bg-blue-200"
                        >
                          <span className="hidden md:block">
                            Detayları Göster
                          </span>
                          <FaArrowRightFromBracket />
                        </button>
                        <MdDeleteForever
                          onClick={() => {
                            setConfirmOpen(true);
                            setRowID(row.id);
                          }}
                          className="hover:opacity-60 cursor-pointer"
                          size={22}
                          color="red"
                        />
                        <FaEdit
                          onClick={() => handleEditClick(row)}
                          className="hover:opacity-60 cursor-pointer"
                          size={22}
                        />
                      </td>
                    </>
                  )}
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className="py-6 text-center text-gray-500">
                  Veri Bulunamadı!
                </td>
              </tr>
            )}

            {/* Yeni Araç Ekleme Satırı */}
            {addingVehicle && (
              <tr className="bg-gray-100">
                <td className="py-2 px-4 text-sm">
                  <input
                    type="text"
                    name="person_name"
                    value={newVehicleData.person_name}
                    onChange={handleNewVehicleChange}
                    className="border rounded px-2 py-1 w-full"
                    placeholder="İsim"
                  />
                </td>
                <td className="py-2 px-4 text-sm text-center w-24"></td>
                <td className="py-2 px-4 text-sm text-center">
                  <input
                    type="text"
                    name="phone"
                    value={newVehicleData.phone}
                    onChange={handleNewVehicleChange}
                    className="border rounded px-2 py-1 w-full text-center"
                    placeholder="Telefon Numarası"
                  />
                </td>
                <td className="py-2 px-4 text-sm text-center">
                  <input
                    type="text"
                    name="plate_number"
                    value={newVehicleData.plate_number}
                    onChange={handleNewVehicleChange}
                    className="border rounded px-2 py-1 w-full text-center"
                    placeholder="Plaka"
                  />
                </td>
                <td className="py-2 px-4 text-sm text-center">--</td>
                <td className="py-2 px-4 text-sm text-center font-bold">--</td>
                <td className="py-2 px-4 text-sm text-center font-bold">--</td>
                <td className="py-2 px-4 text-sm text-center flex justify-center items-center gap-3">
                  <button
                    onClick={createVechileHandler}
                    className="flex items-center gap-1 text-green-500 hover:text-green-700"
                  >
                    <FaSave /> Kaydet
                  </button>
                  <button
                    onClick={() => {
                      setAddingVehicle(false);
                      setNewVehicleData({
                        person_name: "",
                        phone: "",
                        plate_number: "",
                        items: "",
                      });
                    }}
                    className="flex items-center gap-1 text-red-500 hover:text-red-700"
                  >
                    <FaTimes /> İptal
                  </button>
                </td>
              </tr>
            )}

            {/* Add Vehicle Button Satırı */}
            {!addingVehicle && (
              <tr>
                <td colSpan={8} className="py-2 px-4">
                  <div className="w-full flex justify-center">
                    <div className="w-[50%] flex justify-center">
                      <button
                        onClick={() => setAddingVehicle(true)}
                        className="flex items-center  justify-center gap-2 bg-blue-500 text-white px-4 py-2 rounded-2xl hover:bg-blue-500 w-[40%]"
                      >
                        <FaPlus /> Araç Ekle
                      </button>
                    </div>

                    <div className="w-[50%] flex justify-center">
                      <div className="bg-orange-100 border border-orange-500 text-orange-500 p-2 flex justify-center items-center w-1/3">
                        Ödenen:{" "}
                        {Number(amountData.paid).toLocaleString("TR-tr")}₺
                      </div>
                      <div className="bg-red-100 border border-red-500 text-red-500 p-2 flex justify-center items-center w-1/3">
                        Kalan: {Number(amountData.rem).toLocaleString("TR-tr")}₺
                      </div>
                      <div className="bg-green-100 border border-green-500 text-green-500 p-2 flex justify-center items-center w-1/3">
                        Toplam:{" "}
                        {Number(amountData.total).toLocaleString("TR-tr")}₺
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination Kontrolleri */}
      <div className="flex justify-center items-center mt-6 space-x-2">
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className={`px-4 py-2 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 ${
            currentPage === 1 ? "cursor-not-allowed opacity-50" : ""
          }`}
        >
          Prev
        </button>

        {totalPages <= 5 ? (
          // 5 veya daha az sayfa varsa hepsini göster
          pageNumbers.map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-4 py-2 rounded ${
                currentPage === page
                  ? "bg-black text-white"
                  : "bg-gray-200 text-gray-700 hover:bg-gray-300"
              }`}
            >
              {page}
            </button>
          ))
        ) : (
          <>
            {/* 1. sayfa */}
            <button
              onClick={() => handlePageChange(1)}
              className={`px-4 py-2 rounded ${
                currentPage === 1
                  ? "bg-black text-white"
                  : "bg-gray-200 text-gray-700 hover:bg-gray-300"
              }`}
            >
              1
            </button>

            {/* Eğer ortadaki sayfaların ilki 2'den büyükse "..." göster */}
            {pageNumbers[0] > 2 && <span className="px-2">...</span>}

            {/* Orta kısımdaki sayfalar (1 ve last hariç) */}
            {pageNumbers
              .filter((p) => p !== 1 && p !== totalPages)
              .map((page) => (
                <button
                  key={page}
                  onClick={() => handlePageChange(page)}
                  className={`px-4 py-2 rounded ${
                    currentPage === page
                      ? "bg-black text-white"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                  }`}
                >
                  {page}
                </button>
              ))}

            {/* Eğer gösterilen son sayfa totalPages'ten küçükse ... göster */}
            {pageNumbers[pageNumbers.length - 1] < totalPages && (
              <span className="px-2">...</span>
            )}

            {/* Son sayfa */}
            <button
              onClick={() => handlePageChange(totalPages)}
              className={`px-4 py-2 rounded ${
                currentPage === totalPages
                  ? "bg-black text-white"
                  : "bg-gray-200 text-gray-700 hover:bg-gray-300"
              }`}
            >
              {totalPages}
            </button>
          </>
        )}

        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className={`px-4 py-2 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 ${
            currentPage === totalPages ? "cursor-not-allowed opacity-50" : ""
          }`}
        >
          Next
        </button>
      </div>

      {/* Confirm Deletion Modal */}
      <ConfirmModal
        confirmOpen={confirmOpen}
        onClose={onCloseConfirm}
        dataID={rowID}
        handler={deleteVechileHandler}
      />
    </div>
  );
}

export default SingleStore;
