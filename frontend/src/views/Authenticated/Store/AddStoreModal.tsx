import React, { useState, ChangeEvent } from "react";
import Modal from "../../../component/Modal/Modal";
import { MdOutlineClose } from "react-icons/md";
import { setStore } from "../../../services/storeService";
import { toast } from "react-toastify";

interface IAddStoreModal {
  onClose: () => void;
  isOpen?: boolean;
  getStoreData?: any;
}

const AddStoreModal: React.FC<IAddStoreModal> = ({
  isOpen,
  onClose,
  getStoreData,
}) => {
  const [formData, setFormData] = useState({
    name: "",
    phone_number: "",
    person_name: "",
    note: "",
  });

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const createStore = async () => {
    try {
      const response = await setStore(formData);
      if (response) {
        toast.success("Müşteri Başarı ile Oluşturldu.");
      }
    } catch (error) {
      console.log("error: ", error);

      toast.error("Müşteri Oluşturulamadı.");
    } finally {
      getStoreData(1, 10);
      onClose();
      setFormData({
        name: "",
        phone_number: "",
        person_name: "",
        note: "",
      });
    }
  };

  return (
    isOpen && (
      <div className="flex h-full w-full items-center justify-center">
        <Modal
          classname="fixed inset-0 bg-gray-800 bg-opacity-50 p-3 flex items-center justify-center z-40"
          classname2="relative bg-white rounded-3xl w-full md:w-1/3 h-[70%]"
          onClick={onClose}
        >
          <div className="flex flex-col justify-between w-full h-full items-start p-8">
            <div className="w-full">
              <div className="flex flex-row w-full justify-between items-center mb-4">
                <h2 className="text-lg font-bold text-center">Müşteri Ekle</h2>
                <MdOutlineClose size={22} onClick={onClose} />
              </div>
              <div className="space-y-6">
                {/* Name */}
                <div>
                  <label
                    htmlFor="name"
                    className="block text-gray-700 font-medium mb-2"
                  >
                    Adı
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Note */}
                <div>
                  <label
                    htmlFor="note"
                    className="block text-gray-700 font-medium mb-2"
                  >
                    Not
                  </label>
                  <textarea
                    id="note"
                    name="note"
                    value={formData.note}
                    onChange={handleChange}
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  ></textarea>
                </div>

                {/* Person Name */}
                <div>
                  <label
                    htmlFor="person_name"
                    className="block text-gray-700 font-medium mb-2"
                  >
                    Yetkili Adı
                  </label>
                  <input
                    type="text"
                    id="person_name"
                    name="person_name"
                    value={formData.person_name}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Phone Number */}
                <div>
                  <label
                    htmlFor="phone_number"
                    className="block text-gray-700 font-medium mb-2"
                  >
                    Telefon Numarası
                  </label>
                  <input
                    type="text"
                    id="phone_number"
                    name="phone_number"
                    value={formData.phone_number}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Submit Button */}
                <div className="mt-10">
                  <button
                    onClick={createStore}
                    type="submit"
                    className="w-full bg-blue-500 text-white font-medium py-3 px-6 rounded-lg hover:bg-blue-600 transition duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    Kaydet
                  </button>
                </div>
              </div>
            </div>
          </div>
        </Modal>
      </div>
    )
  );
};

export default AddStoreModal;
