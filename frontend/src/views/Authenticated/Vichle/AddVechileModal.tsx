import React, { useState } from "react";
import Modal from "../../../component/Modal/Modal";
import { MdOutlineClose } from "react-icons/md";
import { StoreData } from "../../../types/vechile";
import { toast } from "react-toastify";
import { setVechile } from "../../../services/vechile";

interface IAddVechileModal {
  onClose?: () => void;
  isOpen?: boolean;
  setIsOpen: (value: boolean) => void;
  onSubmit?: (item: StoreData) => void;
  id: any;
  getVechileDatas: any;
}

const AddVechileModal: React.FC<IAddVechileModal> = ({
  isOpen,
  onClose,
  id,
  getVechileDatas,
  setIsOpen,
}) => {
  const [form, setForm] = useState({
    plate_number: "",
    phone: "",
    store_id: id,
    person_name: "",
  });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setForm({ ...form, [name]: value });
  };

  const setVechileHandler = async () => {
    try {
      const response = await setVechile(form);

      if (response) {
        toast.success("parça başarıyla oluşturuldu.");
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setForm({
        plate_number: "",
        phone: "",
        store_id: id,
        person_name: "",
      });
      getVechileDatas();
      setIsOpen(false);
    }
  };

  return (
    isOpen && (
      <div className="flex h-full w-full items-center justify-center">
        <Modal
          classname="fixed inset-0 bg-gray-800 bg-opacity-50 p-3 flex items-center justify-center z-40"
          classname2="relative bg-white rounded-3xl w-full md:w-1/3 h-[50%]"
          onClick={onClose}
        >
          <div className="flex flex-col justify-between w-full h-full items-start p-8">
            <div className="w-full">
              <div className="flex flex-row w-full justify-between items-center mb-4">
                <h2 className="text-lg font-bold text-center">Araç Oluştur</h2>
                <MdOutlineClose size={22} onClick={onClose} />
              </div>
            </div>
            <div className="w-full flex flex-col gap-4">
              <input
                type="text"
                name="person_name"
                value={form.person_name}
                onChange={handleChange}
                placeholder="İsim"
                className="border rounded p-2 w-full"
              />
              <textarea
                name="phone"
                value={form.phone}
                onChange={handleChange}
                placeholder="Telefon Numarası"
                className="border rounded p-2 w-full"
              />
              <input
                type="text"
                name="plate_number"
                value={form.plate_number}
                onChange={handleChange}
                placeholder="Plaka"
                className="border rounded p-2 w-full"
              />
            </div>
            <div className="w-full flex justify-end mt-4 gap-3">
              <button
                onClick={setVechileHandler}
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                Oluştur
              </button>
              <button
                onClick={onClose}
                className="bg-red-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                İptal
              </button>
            </div>
          </div>
        </Modal>
      </div>
    )
  );
};

export default AddVechileModal;
