import { useParams } from "react-router-dom";
import {
  getSingleVechile,
  getVechilePayments,
} from "../../../services/vechile";
import { useEffect, useState, useRef } from "react";
import {
  deleteSingleVechileItem,
  getVechileItems,
  updateSingleVechileItem,
  setVechileItems,
  getItem,
} from "../../../services/itemService";
import { FaEdit } from "react-icons/fa";
import { UpdateVehicleItem, VehicleItem } from "../../../types/vechile";
import { IoIosArrowBack } from "react-icons/io";
import { MdPayments } from "react-icons/md";
import { RiAddBoxFill } from "react-icons/ri";
import AddPaymentModal from "./AddPaymentModal";
import { format } from "date-fns";
import { tr } from "date-fns/locale";
import { toast } from "react-toastify";
import { MdDeleteForever } from "react-icons/md";
import { deletePayment, updatePayment } from "../../../services/paymentService";
import { UpdatePayment } from "../../../types/payment";
import { IoAddCircleOutline, IoSearchOutline } from "react-icons/io5";
import ConfirmModal from "../../../component/Confirm/ConfirmModal";
import Tooltip from "../../../component/Tooltip/Tooltip";
import { Autocomplete, TextField } from "@mui/material";
import { useReactToPrint } from "react-to-print";
import { IoPrint } from "react-icons/io5";

interface Row {
  id: string;
  name: string;
  price: string;
}

function VichleSingle() {
  const contentRef = useRef<HTMLDivElement>(null);
  const reactToPrintFn = useReactToPrint({ contentRef });

  const { id } = useParams();
  const [items, setItems] = useState<VehicleItem[]>([]);
  const [itemsForPrint, setItemsForPrint] = useState<VehicleItem[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [paymentData, setPaymentData] = useState([]);
  const [totalPages, setTotalPages] = useState(1);

  const [loading, setLoading] = useState(false);
  const perPage = 10;
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [rowID, setRowID] = useState("");
  const [itemName, setItemName] = useState<string>("");
  const [printOpen, setPrintOpen] = useState(false);

  const onClose = () => {
    setConfirmOpen(false);
  };

  // Parça düzenleme durumu
  const [editingPartId, setEditingPartId] = useState<string | null>(null);
  const [editPartFormData, setEditPartFormData] = useState<UpdateVehicleItem>({
    name: "",
    quantity: 0,
    price: "",
    note: "",
    is_paid: 2,
  });

  const [amountData, setAmountData] = useState({
    total: "",
    rem: "",
    paid: "",
  });
  // Yeni parça ekleme durumu
  const [addingPart, setAddingPart] = useState<boolean>(false);
  const [newPartData, setNewPartData] = useState<{
    item_id: string;
    name: string;
    quantity: number;
    price: string;
    note: string;
  }>({
    item_id: "",
    name: "",
    quantity: 1,
    price: "",
    note: "",
  });
  const [itemOptions, setItemOptions] = useState<Row[]>([]);
  const [queryKey, setQueryKey] = useState("");
  const [queryKey2, setQueryKey2] = useState("");
  // Ödeme düzenleme durumu
  const [editingRowId, setEditingRowId] = useState<string | null>(null);
  const [editFormData, setEditFormData] = useState<UpdatePayment>({
    amount: "",
    due_date: "",
    is_paid: 2,
    note: "",
  });

  const [paymentModalOpen, setPaymentModalOpen] = useState<boolean>(false);

  const [newItem, setNewItem] = useState(false);

  const [personName, setPersonName] = useState<string>("");
  // const [personPhone, setPersonPhone] = useState<string>("");

  const onClosePaymentModalOpen = () => {
    setPaymentModalOpen(false);
  };

  const getVechileSingleData = async () => {
    try {
      if (id) {
        const response = await getSingleVechile(id);
        setPersonName(response.data.person_name);
        // setPersonPhone(response.data.phone);
        console.log(response);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const getVechileItemsData = async () => {
    try {
      setLoading(true);
      if (id) {
        const response = await getVechileItems(
          id,
          currentPage,
          perPage,
          queryKey
        );
        setItems(response.data.rows || []);
        setTotalPages(response.data.total_pages || 1);
        setAmountData({
          total: response.data.total_amount,
          paid: response.data.paid_amount,
          rem: response.data.remaining_amount,
        });
      }
    } catch (error) {
      toast.error("Veriler alınamadı.");
    } finally {
      setLoading(false);
    }
  };
  const getVechileItemsDataForPrint = async () => {
    try {
      setLoading(true);
      if (id) {
        const response = await getVechileItems(id, currentPage, 200, queryKey);
        setItemsForPrint(response.data.rows || []);
      }
    } catch (error) {
      toast.error("Veriler alınamadı.");
    } finally {
      setLoading(false);
    }
  };

  const getVechilePaymentsHandler = async () => {
    try {
      if (id) {
        const response = await getVechilePayments(id);
        setPaymentData(response?.data);
      }
    } catch (error) {
      toast.error("Ödemeler alınamadı.");
    }
  };

  const deletePaymentHandler = async (id: string) => {
    try {
      const response = await deletePayment(id);
      if (response) {
        toast.success("Ödeme başarıyla silindi.");
        getVechilePaymentsHandler();
      }
    } catch (error) {
      toast.error("Ödeme silinemedi.");
    } finally {
      getVechileItemsData();
    }
  };

  useEffect(() => {
    getVechileItemsData();
    getVechilePaymentsHandler();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, queryKey]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  useEffect(() => {
    fetchItemOptions();
  }, [queryKey2]);
  // Parça seçeneklerini getir
  const fetchItemOptions = async () => {
    try {
      const response = await getItem(1, 10, queryKey2); // Tüm parçaları getir
      setItemOptions(response.data.rows);
    } catch (error) {
      console.error("Parçalar alınamadı:", error);
    }
  };

  // Yeni parça ekleme işlemleri
  const handleAddPartClick = () => {
    setAddingPart(true);
  };

  const handleNewPartChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = event.target;
    setNewPartData({
      ...newPartData,
      [name]: name === "quantity" ? Number(value) : value,
    });
  };
  const handleNewPartChange2 = (
    event: React.SyntheticEvent,
    value: Row | null
  ) => {
    console.log(event);
    setNewPartData({
      ...newPartData,
      item_id: value ? value.id : "",
      name: value ? value.name : "",
    });
  };
  const handleSaveNewPart = async () => {
    try {
      if (id) {
        const selectedItem = itemOptions.find(
          (item) => item.id === newPartData.item_id
        );

        const response = await setVechileItems({
          vehicle_id: id,
          item_id: newPartData.item_id ? newPartData.item_id : "",
          quantity: newPartData.quantity,
          price: selectedItem
            ? (Number(selectedItem.price) * newPartData.quantity).toString()
            : newPartData.price,
          note: newPartData.note,
          isPaid: 2,
          name: newPartData.name ? newPartData.name : itemName,
        });
        if (response) {
          toast.success("Parça başarıyla eklendi.");
          setAddingPart(false);
          setNewPartData({
            item_id: "",
            name: "",
            quantity: 1,
            price: "",
            note: "",
          });
          getVechileItemsData();
        }
      }
    } catch (error) {
      console.error("Parça eklenemedi:", error);
      toast.error("Parça eklenemedi.");
    } finally {
      setItemName("");
      setNewItem(false);
      setQueryKey2("");
    }
  };

  const handleCancelNewPart = () => {
    setAddingPart(false);
    setNewPartData({
      item_id: "",
      name: "",
      quantity: 1,
      price: "",
      note: "",
    });
  };

  // Parça düzenleme işlemleri
  const handleEditPartClick = (rowData: VehicleItem) => {
    setEditingPartId(rowData.id);
    setEditPartFormData({
      name: rowData.name,
      quantity: rowData.quantity,
      price: rowData.price.toString(),
      note: rowData.note,
      is_paid: rowData.is_paid,
    });
  };

  const handleEditPartFormChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { name, value } = event.target;
    setEditPartFormData({
      ...editPartFormData,
      [name]: name === "quantity" ? Number(value) : value,
    });
  };

  const handleSavePartClick = async (id: string) => {
    try {
      const response = await updateSingleVechileItem(id, {
        ...editPartFormData,
        quantity: Number(editPartFormData.quantity),
        price: (
          Number(editPartFormData.price) * editPartFormData.quantity
        ).toString(),
      });
      if (response) {
        toast.success("Parça başarıyla güncellendi.");
        setEditingPartId(null);
        getVechileItemsData();
      }
    } catch (error) {
      toast.error("Parça güncellenemedi.");
    }
  };

  const handleCancelPartClick = () => {
    setEditingPartId(null);
  };

  // Ödeme düzenleme işlemleri
  const handleEditClick = (rowData: any) => {
    setEditingRowId(rowData.id);
    setEditFormData({
      amount: rowData.amount,
      due_date: rowData.due_date,
      is_paid: rowData.is_paid,
      note: rowData.note,
    });
  };

  const handleEditFormChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = event.target;
    setEditFormData({
      ...editFormData,
      [name]: value,
    });
  };

  const handleSaveClick = async (id: string) => {
    try {
      const response = await updatePayment(id, {
        ...editFormData,
        is_paid: Number(editFormData.is_paid),
      });
      if (response) {
        toast.success("Ödeme başarıyla güncellendi.");
        setEditingRowId(null);
        getVechilePaymentsHandler();
      }
    } catch (error) {
      toast.error("Ödeme güncellenemedi.");
    } finally {
      getVechileItemsData();
    }
  };

  const delteVechileItemHandler = (id: string) => {
    try {
      deleteSingleVechileItem(id);
      toast.success("İşlem başarılı");
    } catch (error) {
      toast.error("İşlem başarısız");
    } finally {
      setConfirmOpen(false);
      setTimeout(() => {
        getVechileItemsData();
      }, 500);
    }
  };

  const handleCancelClick = () => {
    setEditingRowId(null);
  };

  useEffect(() => {
    getVechilePaymentsHandler();
    getVechileItemsDataForPrint();
  }, [printOpen]);
  useEffect(() => {
    getVechileSingleData();
  }, []);

  // Pagination logic (benzer mantık Item.tsx den)
  let pageNumbers: number[] = [];
  if (totalPages <= 5) {
    // 5 veya daha az sayfa varsa hepsini göster
    pageNumbers = Array.from({ length: totalPages }, (_, i) => i + 1);
  } else {
    // Daha fazla sayfa varsa
    const visiblePages = 3;
    let startPage = Math.max(currentPage - Math.floor(visiblePages / 2), 1);
    let endPage = startPage + visiblePages - 1;

    if (endPage > totalPages) {
      endPage = totalPages;
      startPage = Math.max(endPage - visiblePages + 1, 1);
    }

    pageNumbers = [];
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }
  }

  return (
    <div className="w-full">
      <div className="w-full  flex justify-between gap-3">
        <button
          onClick={() => window.history.back()}
          className="flex items-center gap-2 text-gray-800 font-medium px-4 py-2 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-400 no-print"
        >
          <IoIosArrowBack /> Geri Dön
        </button>

        <div className="text-xl font-semibold text-gray-600 no-print">
          Parça Tablosu
        </div>
        <div>
          <style>{`
    @page {
      size: 80mm;
      margin: 0;
    }

    @media print {
      html, body {
        width: 80mm;
        margin: 0;
        padding: 0;
      }
      
      #no-print {
        display: none !important;
      }

      #print-only {
        display: block !important;
      }
    }

    /* Normal görünümde, #print-only öğesini gizliyoruz. */
    #print-only {
      display: none;
    }
  `}</style>

          <button
            //  onClick={() => console.log(paymentData)}
            onClick={() => {
              setPrintOpen(!printOpen);
              reactToPrintFn && reactToPrintFn();
            }}
            // onClick={() => console.log(items, paymentData)}
            className="flex w-32 justify-center gap-2 items-center p-2 bg-blue-200 border border-blue-500 text-blue-600 rounded-md hover:opacity-80"
            id="no-print"
          >
            <IoPrint /> Yazdır
          </button>

          <div
            ref={contentRef}
            id="print-only"
            className="p-4 bg-white rounded-lg shadow-md"
          >
            <div className="w-full text-center font-bold text-xl border-b pb-2 mb-4">
              <div>Kalkan OTO</div>
              <div className="text-sm font-normal">0541 632 83 13</div>
            </div>
            <div className="w-full text-center font-bold text-lg underline mb-4">
              {personName}
            </div>

            <div className="w-full grid grid-cols-3 font-bold underline text-center py-2 border-b">
              <div>Parça İsmi</div>
              <div>Adet</div>
              <div>Fiyat (₺)</div>
            </div>
            {itemsForPrint.length > 0 &&
              itemsForPrint.map((m) => (
                <div className="w-full flex justify-between p-2">
                  <div className="text-center">{m.name}</div>
                  <div className="text-center">{m.quantity}</div>
                  <div className="text-center">{m.price}₺</div>
                </div>
              ))}
            <hr />
            <div className="w-full p-2">
              <div className="w-full flex justify-between">
                <div>Ödenen Tutar</div>
                <div className="font-bold">{amountData.paid}₺</div>
              </div>
              <div className="w-full flex justify-between">
                <div>Kalan Tutar</div>
                <div className="font-bold">{amountData.rem}₺</div>
              </div>
              <div className="w-full flex justify-between">
                <div>Toplam Tutar</div>
                <div className="font-bold">{amountData.total}₺</div>
              </div>
            </div>
            <hr />
            {paymentData?.length > 0 && (
              <div className="w-full flex p-2 font-bold text-center underline">
                Ödemeler
              </div>
            )}
            <div className="w-full p-2">
              {paymentData?.length > 0 &&
                paymentData.map((m: any) => (
                  <tr
                    key={m.id}
                    className="hover:bg-gray-50 transition-colors border-b last:border-0"
                  >
                    {" "}
                    <td className="px-2 py-2">
                      <span
                        className={`px-2 py-1 rounded-full text-sm font-bold`}
                      >
                        {m.is_paid === 1 ? "Ödendi" : "Ödenmedi"}
                      </span>
                    </td>
                    <td className="px-2 py-2 text-gray-900">
                      {m?.due_date && m.due_date !== ""
                        ? format(new Date(m.due_date), "dd/MM/yyyy", {
                            locale: tr,
                          })
                        : m?.due_date === "" && m?.payment_date === ""
                        ? "Tarih Yok"
                        : m?.payment_date && m.payment_date !== ""
                        ? format(new Date(m.payment_date), "dd/MM/yyyy", {
                            locale: tr,
                          })
                        : ""}
                    </td>
                    <td className="px-2 py-2 text-gray-900 font-bold">
                      {parseFloat(m.amount).toFixed(2)}₺
                    </td>
                  </tr>
                ))}
            </div>
          </div>
        </div>
      </div>

      <div className="w-full flex flex-col md:flex-row mt-2">
        <div className="w-full md:w-4/6">
          <div className="overflow-x-auto border rounded-xl">
            <div className="flex items-center border  rounded-lg px-3 py-1 bg-gray-100  focus-within:ring-1 shadow-sm w-full md:w-[40%] m-1">
              <IoSearchOutline className="text-gray-500 mr-2" size={20} />
              <input
                type="text"
                placeholder="Araç Ara..."
                className="flex-1 outline-none bg-transparent text-gray-700 placeholder-gray-400"
                value={queryKey}
                onChange={(e: any) => setQueryKey(e.target.value)}
              />
            </div>
            <table className="table-auto w-full ">
              <thead className="w-full">
                <tr className="bg-gray-50">
                  <th className="py-2 px-2 md:py-3 md:px-4 font-semibold text-gray-700 text-center w-1/6">
                    PARÇA İSİMİ
                  </th>
                  <th className="py-2 px-2 md:py-3 md:px-4 font-semibold text-gray-700 text-center w-1/6">
                    ADET
                  </th>
                  <th className="py-2 px-2 md:py-3 md:px-4 font-semibold text-gray-700 text-center w-1/6">
                    FİYAT
                  </th>
                  <th className="py-2 px-2 md:py-3 md:px-4 font-semibold text-gray-700 text-center w-1/6">
                    NOT
                  </th>
                  <th className="py-2 px-2 md:py-3 md:px-4 font-semibold text-gray-700 text-center w-1/6">
                    OLUŞTURULMA TARİHİ
                  </th>
                  <th className="py-2 px-2 md:py-3 md:px-4 font-semibold text-gray-700 text-center w-1/6">
                    AKSİYONLAR
                  </th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={6} className="py-6 text-center text-gray-500">
                      Loading...
                    </td>
                  </tr>
                ) : items?.length > 0 ? (
                  items.map((row) => (
                    <tr
                      key={row?.id}
                      className="hover:bg-gray-100 bg-white border-b last:border-0 h-16"
                    >
                      {editingPartId === row.id ? (
                        // Düzenleme satırı
                        <>
                          <td className="py-2 px-2 text-sm">
                            <input
                              type="text"
                              name="name"
                              value={editPartFormData.name}
                              onChange={handleEditPartFormChange}
                              className="border rounded px-2 py-1 w-full"
                            />
                          </td>
                          <td className="py-2 px-2 text-sm text-center">
                            <input
                              type="number"
                              name="quantity"
                              value={editPartFormData.quantity}
                              onChange={handleEditPartFormChange}
                              className="border rounded px-2 py-1 w-full text-center"
                            />
                          </td>
                          <td className="py-2 px-2 text-sm text-center font-bold">
                            <input
                              type="text"
                              name="price"
                              value={editPartFormData.price}
                              onChange={handleEditPartFormChange}
                              className="border rounded px-2 py-1 w-full text-right"
                            />
                          </td>
                          <td className="py-2 px-2 text-sm hidden md:table-cell">
                            <input
                              type="text"
                              name="note"
                              value={editPartFormData.note}
                              onChange={handleEditPartFormChange}
                              className="border rounded px-2 py-1 w-full"
                            />
                          </td>
                          <td className="py-2 px-2 text-sm text-center">
                            {new Date(row.CreatedAt).toLocaleString()}
                          </td>
                          <td className="py-2 px-2 text-sm text-center">
                            <div className="flex gap-1 justify-center">
                              <button
                                onClick={() => {
                                  handleSavePartClick(row.id);
                                }}
                                className="text-green-500 hover:text-green-700"
                              >
                                Kaydet
                              </button>
                              <button
                                onClick={handleCancelPartClick}
                                className="text-red-500 hover:text-red-700"
                              >
                                İptal
                              </button>
                            </div>
                          </td>
                        </>
                      ) : (
                        <>
                          <td className="py-2 px-2 text-sm text-center w-40">
                            {row.name}
                          </td>
                          <td className="py-2 px-2 text-sm text-center w-16">
                            {row.quantity}
                          </td>
                          <td className="py-2 px-2 text-sm text-center font-bold w-24">
                            {row.price} ₺
                          </td>

                          <td className="py-2 px-4 text-sm text-center cursor-pointer">
                            <Tooltip
                              position="bottom"
                              content={<div className="w-56">{row.note}</div>}
                            >
                              {row.note.length > 20
                                ? row.note.slice(0, 10) + "..."
                                : row.note}
                            </Tooltip>
                          </td>

                          <td className="py-2 px-2 text-sm text-center w-32">
                            {new Date(row.CreatedAt).toLocaleString()}
                          </td>
                          <td className="py-2 px-2 text-sm text-center w-24">
                            <div className="flex gap-1 justify-center">
                              <MdDeleteForever
                                className="hover:opacity-60 cursor-pointer"
                                size={22}
                                color="red"
                                onClick={() => {
                                  setRowID(row.id);
                                  setConfirmOpen(true);
                                }}
                              />
                              <FaEdit
                                onClick={() => handleEditPartClick(row)}
                                className="cursor-pointer"
                                size={19}
                              />
                            </div>
                          </td>
                        </>
                      )}
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={6} className="py-6 text-center text-gray-500">
                      Veri Bulunamadı!
                    </td>
                  </tr>
                )}
              </tbody>
              {addingPart && (
                <tr className="bg-gray-100">
                  {/* Yeni parça ekleme satırı */}
                  <td className="py-2 px-2 text-sm">
                    {false ? (
                      <input
                        type="text"
                        name="name"
                        placeholder="Yeni Parça İsmi"
                        value={newPartData.name}
                        onChange={handleNewPartChange}
                        className="border rounded px-2 py-1 w-full text-center"
                      />
                    ) : (
                      <Autocomplete
                        inputValue={
                          itemName.length > 0 ? itemName : newPartData.name
                        }
                        options={itemOptions}
                        getOptionLabel={(option) =>
                          option.name || "İsimlendirilmemiş parça"
                        }
                        isOptionEqualToValue={(option, value) =>
                          option.id === value.id
                        }
                        value={
                          itemOptions.find(
                            (item) => item.id === newPartData.item_id
                          ) || null
                        }
                        onChange={handleNewPartChange2}
                        renderInput={(params) => (
                          <TextField
                            onChange={(e: any) => {
                              setItemName(e.target.value);
                              setNewItem(true);
                              setQueryKey2(e.target.value);
                            }}
                            {...params}
                            label="Parça seçin"
                            placeholder="Parça ara"
                            variant="outlined"
                          />
                        )}
                      />
                    )}
                  </td>
                  <td className="py-2 px-2 text-sm text-center">
                    <input
                      type="number"
                      name="quantity"
                      placeholder="miktar"
                      value={newPartData.quantity}
                      onChange={handleNewPartChange}
                      className="border rounded px-2 py-1 w-full text-center"
                    />
                  </td>
                  {newItem ? (
                    <td className="py-2 px-2 text-sm hidden md:table-cell">
                      <input
                        type="text"
                        name="price"
                        placeholder="fiyat"
                        value={newPartData.price}
                        onChange={handleNewPartChange}
                        className="border rounded px-2 py-1 w-full"
                      />
                    </td>
                  ) : (
                    <td className="py-2 px-2 text-sm text-center font-bold">
                      {(() => {
                        const selectedItem = itemOptions.find(
                          (item) => item.id === newPartData.item_id
                        );
                        return selectedItem
                          ? (
                              parseFloat(selectedItem.price) *
                              newPartData.quantity
                            ).toFixed(2) + " ₺"
                          : "0 ₺";
                      })()}
                    </td>
                  )}

                  <td className="py-2 px-2 text-sm hidden md:table-cell">
                    <input
                      type="text"
                      name="note"
                      placeholder="not"
                      value={newPartData.note}
                      onChange={handleNewPartChange}
                      className="border rounded px-2 py-1 w-full"
                    />
                  </td>
                  <td className="py-2 px-2 text-sm text-center">--</td>
                  <td className="py-2 px-2 text-sm text-center">
                    <div className="flex gap-1 justify-center">
                      <button
                        onClick={() => {
                          handleSaveNewPart();
                          setNewItem(false);
                          setQueryKey2("");
                        }}
                        className="text-green-500 hover:text-green-700"
                      >
                        Kaydet
                      </button>
                      <button
                        onClick={() => {
                          handleCancelNewPart();
                          setQueryKey2("");
                          setNewItem(false);
                        }}
                        className="text-red-500 hover:text-red-700"
                      >
                        İptal
                      </button>
                    </div>
                  </td>
                </tr>
              )}
              {!addingPart && (
                <tr>
                  <td colSpan={6} className="py-2 px-4">
                    <div className="w-full flex justify-center">
                      <div className="w-[50%] flex justify-center">
                        <button
                          onClick={handleAddPartClick}
                          className="flex items-center  justify-center gap-2 bg-blue-500 text-white  rounded-2xl hover:bg-blue-500 w-[80%]"
                        >
                          <IoAddCircleOutline size={22} /> Parça Ekle
                        </button>
                      </div>

                      <div className="w-[50%] flex justify-center">
                        <div className="bg-orange-100 border border-orange-500 text-orange-500 p-2 flex justify-center items-center w-1/3">
                          Ödenen:{" "}
                          {Number(amountData.paid).toLocaleString("TR-tr")}₺
                        </div>
                        <div className="bg-red-100 border border-red-500 text-red-500 p-2 flex justify-center items-center w-1/3">
                          Kalan:{" "}
                          {Number(amountData.rem).toLocaleString("TR-tr")}₺
                        </div>
                        <div className="bg-green-100 border border-green-500 text-green-500 p-2 flex justify-center items-center w-1/3">
                          Toplam:{" "}
                          {Number(amountData.total).toLocaleString("TR-tr")}₺
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              )}
            </table>
          </div>

          {/* Sayfalama Kontrolleri */}
          <div className="flex justify-center items-center mt-6 space-x-2">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className={`px-4 py-2 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 ${
                currentPage === 1 ? "cursor-not-allowed opacity-50" : ""
              }`}
            >
              Prev
            </button>

            {totalPages <= 5 ? (
              // 5 veya daha az sayfa varsa hepsini göster
              pageNumbers.map((page) => (
                <button
                  key={page}
                  onClick={() => handlePageChange(page)}
                  className={`px-4 py-2 rounded ${
                    currentPage === page
                      ? "bg-black text-white"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                  }`}
                >
                  {page}
                </button>
              ))
            ) : (
              <>
                {/* 1. sayfa */}
                <button
                  onClick={() => handlePageChange(1)}
                  className={`px-4 py-2 rounded ${
                    currentPage === 1
                      ? "bg-black text-white"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                  }`}
                >
                  1
                </button>

                {/* Eğer ortadaki sayfaların ilki 2'den büyükse "..." göster */}
                {pageNumbers[0] > 2 && <span className="px-2">...</span>}

                {/* Orta kısımdaki sayfalar (1 ve last hariç) */}
                {pageNumbers
                  .filter((p) => p !== 1 && p !== totalPages)
                  .map((page) => (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`px-4 py-2 rounded ${
                        currentPage === page
                          ? "bg-black text-white"
                          : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                      }`}
                    >
                      {page}
                    </button>
                  ))}

                {/* Eğer gösterilen son sayfa totalPages'ten küçükse ... göster */}
                {pageNumbers[pageNumbers.length - 1] < totalPages && (
                  <span className="px-2">...</span>
                )}

                {/* Son sayfa */}
                <button
                  onClick={() => handlePageChange(totalPages)}
                  className={`px-4 py-2 rounded ${
                    currentPage === totalPages
                      ? "bg-black text-white"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                  }`}
                >
                  {totalPages}
                </button>
              </>
            )}

            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className={`px-4 py-2 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 ${
                currentPage === totalPages
                  ? "cursor-not-allowed opacity-50"
                  : ""
              }`}
            >
              Next
            </button>
          </div>
        </div>

        <div className="w-full md:w-2/6 mt-6 md:mt-0 md:ml-4">
          <div className="border rounded-xl h-full flex flex-col">
            <div className="w-full p-4 bg-blue-200 text-blue-600 font-bold text-xl flex justify-between items-center">
              <div className="flex items-center gap-2">
                <MdPayments size={24} />
                <span>Ödemeler</span>
              </div>
              <RiAddBoxFill
                className="cursor-pointer hover:scale-110 transition-transform"
                onClick={() => setPaymentModalOpen(true)}
                size={28}
              />
            </div>
            <div className="flex-grow overflow-y-auto p-2">
              <table className="min-w-full bg-white">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-2 py-2 text-left text-gray-600 font-medium">
                      Tutar
                    </th>
                    <th className="px-2 py-2 text-left text-gray-600 font-medium hidden md:table-cell">
                      Not
                    </th>
                    <th className="px-2 py-2 text-left text-gray-600 font-medium">
                      Ödeme Tarihi
                    </th>
                    <th className="px-2 py-2 text-left text-gray-600 font-medium">
                      Durum
                    </th>
                    <th className="px-2 py-2 text-left text-gray-600 font-medium">
                      Aksiyonlar
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {paymentData?.length > 0 ? (
                    paymentData.map((m: any) => (
                      <tr
                        key={m.id}
                        className="hover:bg-gray-50 transition-colors border-b last:border-0"
                      >
                        {editingRowId === m.id ? (
                          <>
                            <td className="px-2 py-2 text-gray-900">
                              <input
                                type="text"
                                name="amount"
                                value={editFormData.amount}
                                onChange={handleEditFormChange}
                                className="w-24 border rounded px-2 py-1"
                              />
                            </td>
                            <td className="px-2 py-2 text-gray-900 hidden md:table-cell">
                              <input
                                type="text"
                                name="note"
                                value={editFormData.note}
                                onChange={handleEditFormChange}
                                className="w-24 border rounded px-2 py-1 "
                              />
                            </td>
                            <td className="px-2 py-2 text-gray-900">
                              <input
                                type="date"
                                name="due_date"
                                value={editFormData.due_date}
                                onChange={handleEditFormChange}
                                className="border rounded px-2 py-1"
                              />
                            </td>
                            <td className="px-2 py-2 whitespace-nowrap">
                              <select
                                name="is_paid"
                                value={editFormData.is_paid}
                                onChange={handleEditFormChange}
                                className="w-24 border rounded px-2 py-1"
                              >
                                <option value={1}>Ödendi</option>
                                <option value={2}>Ödenmedi</option>
                              </select>
                            </td>
                            <td className="px-2 py-2 text-gray-900">
                              <div className="flex gap-1">
                                <button
                                  onClick={() => handleSaveClick(m.id)}
                                  className="text-green-500 hover:text-green-700"
                                >
                                  Kaydet
                                </button>
                                <button
                                  onClick={handleCancelClick}
                                  className="text-red-500 hover:text-red-700"
                                >
                                  İptal
                                </button>
                              </div>
                            </td>
                          </>
                        ) : (
                          <>
                            <td className="px-2 py-2 text-gray-900">
                              {parseFloat(m.amount).toFixed(2)} TL
                            </td>
                            <Tooltip
                              position="bottom"
                              content={<div className="w-56">{m.note}</div>}
                            >
                              <td className="py-2 px-4 text-sm text-center  cursor-pointer">
                                {m.note.length > 20
                                  ? m.note.slice(0, 10) + "..."
                                  : m.note}
                              </td>
                            </Tooltip>
                            <td className="px-2 py-2 text-gray-900">
                              {m?.due_date && m.due_date !== ""
                                ? format(new Date(m.due_date), "dd/MM/yyyy", {
                                    locale: tr,
                                  })
                                : m?.due_date === "" && m?.payment_date === ""
                                ? "Tarih Yok"
                                : m?.payment_date && m.payment_date !== ""
                                ? format(
                                    new Date(m.payment_date),
                                    "dd/MM/yyyy",
                                    {
                                      locale: tr,
                                    }
                                  )
                                : ""}
                            </td>
                            <td className="px-2 py-2">
                              <span
                                className={`px-2 py-1 rounded-full text-sm ${
                                  m.is_paid === 1
                                    ? "bg-green-100 text-green-600"
                                    : "bg-red-100 text-red-600"
                                }`}
                              >
                                {m.is_paid === 1 ? "Ödendi" : "Ödenmedi"}
                              </span>
                            </td>
                            <td className="px-2 py-2 text-gray-900">
                              <div className="flex gap-1">
                                <MdDeleteForever
                                  color="red"
                                  onClick={() => deletePaymentHandler(m.id)}
                                  size={19}
                                  className="cursor-pointer"
                                />
                                <FaEdit
                                  onClick={() => handleEditClick(m)}
                                  className="cursor-pointer"
                                  size={19}
                                />
                              </div>
                            </td>
                          </>
                        )}
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td
                        colSpan={5}
                        className="py-6 text-center text-gray-500"
                      >
                        Veri Bulunamadı!
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* "AddVechileItemModal" bileşenini kaldırdık */}
      <AddPaymentModal
        onClose={onClosePaymentModalOpen}
        isOpen={paymentModalOpen}
        vehicleID={id}
        getVechilePaymentsHandler={getVechilePaymentsHandler}
        getVechileItemsData={getVechileItemsData}
      />
      <ConfirmModal
        confirmOpen={confirmOpen}
        onClose={onClose}
        dataID={rowID}
        handler={delteVechileItemHandler}
      />
    </div>
  );
}

export default VichleSingle;
