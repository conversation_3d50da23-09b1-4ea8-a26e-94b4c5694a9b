import React, { useState } from "react";
import Modal from "../../../component/Modal/Modal";
import { MdOutlineClose } from "react-icons/md";
import { toast } from "react-toastify";
import moment from "moment";
import { CreatePayment } from "../../../types/payment";
import { createPayment } from "../../../services/paymentService";

interface IAddPaymentModal {
  onClose?: any;
  isOpen?: boolean;
  vehicleID: any;
  getVechilePaymentsHandler?: any;
  getVechileItemsData?: any;
}

const AddPaymentModal: React.FC<IAddPaymentModal> = ({
  onClose,
  isOpen,
  vehicleID,
  getVechilePaymentsHandler,
  getVechileItemsData,
}) => {
  const [form, setForm] = useState<CreatePayment>({
    amount: "",
    is_paid: 2, // 1: Paid, 2: Not Paid/Deferred
    due_date: "",
    vehicle_id: vehicleID,
    note: "",
  });

  // State for the Deferred Payment toggle switch
  const [isDeferredPayment, setIsDeferredPayment] = useState(false);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | any>
  ) => {
    const { name, value, checked } = e.target;

    if (name === "isDeferredPayment") {
      setIsDeferredPayment(checked);
      if (checked) {
        // If Deferred Payment is selected
        setForm({
          ...form,
          is_paid: 2, // Set to 'Not Paid' or 'Deferred'
        });
      } else {
        // If Deferred Payment is deselected
        setForm({
          ...form,
          due_date: "",
          is_paid: 1, // Default to 'Paid' when not deferred
        });
      }
    } else if (name === "isPaid") {
      setForm({
        ...form,
        is_paid: checked ? 1 : 2, // 1: Paid, 2: Not Paid
      });
    } else {
      setForm({
        ...form,
        [name]: value,
      });
    }
  };

  const setPaymentHandler = async () => {
    try {
      const response = await createPayment(form);

      if (response) {
        toast.success("Ödeme başarıyla oluşturuldu.");
      }
    } catch (error) {
      toast.error("Ödeme oluşturulamadı.");
    } finally {
      onClose();
      getVechilePaymentsHandler();
      getVechileItemsData();
      setForm({
        amount: "",
        is_paid: 2,
        due_date: "",
        vehicle_id: vehicleID,
        note: "",
      });
      setIsDeferredPayment(false); // Reset the Deferred Payment toggle
    }
  };

  return (
    isOpen && (
      <div className="flex h-full w-full items-center justify-center">
        <Modal
          classname="fixed inset-0 bg-gray-800 bg-opacity-50 p-3 flex items-center justify-center z-40"
          classname2="relative bg-white rounded-3xl w-full md:w-1/3 "
          onClick={onClose}
        >
          <div className="flex flex-col justify-between w-full h-full items-start p-5 overflow-auto">
            <div className="w-full">
              <div className="flex flex-row w-full justify-between items-center mb-4">
                <h2 className="text-lg font-bold text-center">Ödeme Oluştur</h2>
                <MdOutlineClose size={22} onClick={onClose} />
              </div>
            </div>
            <div className="w-full flex flex-col gap-4">
              <div>
                <label htmlFor="amount">Fiyat</label>
                <input
                  type="text"
                  id="amount"
                  name="amount"
                  value={form.amount}
                  onChange={handleChange}
                  className="border rounded p-2 w-full"
                />
              </div>

              <div>
                <label htmlFor="note">Not</label>
                <textarea
                  id="note"
                  name="note"
                  value={form.note}
                  onChange={handleChange}
                  className="border rounded p-2 w-full"
                />
              </div>

              {/* Deferred Payment Toggle Switch */}
              <div>
                <label htmlFor="isDeferredPayment" className="block mb-2">
                  İleri Tarihli Ödeme
                </label>
                <div className="flex items-center">
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      id="isDeferredPayment"
                      name="isDeferredPayment"
                      checked={isDeferredPayment}
                      onChange={handleChange}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-500 rounded-full peer peer-checked:bg-blue-600 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all"></div>
                    <span className="ml-3 text-sm font-medium text-gray-900">
                      {isDeferredPayment ? "Evet" : "Hayır"}
                    </span>
                  </label>
                </div>
              </div>

              {/* Due Date Selection for Deferred Payment */}
              {isDeferredPayment && (
                <div>
                  <label htmlFor="due_date">Ödeme Tarihi</label>
                  <input
                    type="date"
                    id="due_date"
                    name="due_date"
                    value={
                      form.due_date
                        ? moment(form.due_date).format("YYYY-MM-DD")
                        : ""
                    }
                    onChange={handleChange}
                    className="border rounded p-2 w-full"
                  />
                </div>
              )}

              {/* Payment Status Toggle Switch */}
              {!isDeferredPayment && (
                <div>
                  <label htmlFor="isPaid" className="block mb-2">
                    Ödeme Durumu
                  </label>
                  <div className="flex items-center">
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        id="isPaid"
                        name="isPaid"
                        checked={form.is_paid === 1}
                        onChange={handleChange}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-green-500 rounded-full peer peer-checked:bg-green-600 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all"></div>
                      <span className="ml-3 text-sm font-medium text-gray-900">
                        {form.is_paid === 1 ? "Ödendi" : "Ödenmedi"}
                      </span>
                    </label>
                  </div>
                </div>
              )}
            </div>

            <div className="w-full flex justify-end mt-4 gap-3">
              <button
                onClick={setPaymentHandler}
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                Oluştur
              </button>
              <button
                onClick={onClose}
                className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
              >
                İptal
              </button>
            </div>
          </div>
        </Modal>
      </div>
    )
  );
};

export default AddPaymentModal;
