import React, { useEffect, useState } from "react";
import Modal from "../../../component/Modal/Modal";
import { MdOutlineClose } from "react-icons/md";
import { toast } from "react-toastify";
import { getItem, setVechileItems } from "../../../services/itemService";

interface Row {
  id: string;
  CreatedAt: string;
  UpdatedAt: string;
  DeletedAt: string | null;
  updated_by: string;
  updated_date: string | null;
  deleted_by: string;
  deleted_date: string | null;
  maked_by: string;
  name: string;
  price: string;
  quantity: number;
  note: string;
}

interface IAddVechileItemModal {
  onClose: () => void;
  isOpen?: boolean;
  setIsOpen?: (value: boolean) => void;
  vehicleID?: string;
  getVechileItemDatas?: any;
}

const AddVechileItemModal: React.FC<IAddVechileItemModal> = ({
  isOpen,
  onClose,
  vehicleID,
  getVechileItemDatas,
}) => {
  const [itemData, setItemData] = useState<Row[]>([]);
  const [selectedValue, setSelectedValue] = useState<any>();

  const [payloadItem, setPayloadItem] = useState<any>();

  const handleChange = (e: any) => {
    setSelectedValue(e.target.value);
  };

  const fetchItems = async (page: number) => {
    try {
      const response = await getItem(page, 10);
      setItemData(response.data.rows);
    } catch (error) {
      console.error("Error fetching items:", error);
    }
  };

  const setVechileItemsHandler = async () => {
    try {
      if (vehicleID) {
        const response = await setVechileItems({
          vehicle_id: vehicleID,
          item_id: payloadItem?.id,
          quantity: 1,
          price: payloadItem?.price,
          note: payloadItem?.note,
          isPaid: 1,
          name: payloadItem?.name,
        });
        if (response) {
          toast.success("parça başarıyla eklendi.");
        }
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      getVechileItemDatas();
      onClose();
    }
  };
  useEffect(() => {
    fetchItems(1);
  }, []);

  useEffect(() => {
    setPayloadItem(itemData.filter((m) => m.id === selectedValue)[0]);
  }, [selectedValue]);
  return (
    isOpen && (
      <div className="flex h-full w-full items-center justify-center">
        <Modal
          classname="fixed inset-0 bg-gray-800 bg-opacity-50 p-3 flex items-center justify-center z-40"
          classname2="relative bg-white rounded-3xl w-full md:w-1/4 h-[20%]"
          onClick={onClose}
        >
          <div className="flex flex-col justify-between w-full h-full items-start p-8">
            <div className="w-full">
              <div className="flex flex-row w-full justify-between items-center mb-4">
                <h2 className="text-lg font-bold text-center">Parça Ekle</h2>
                <MdOutlineClose size={22} onClick={onClose} />
              </div>
            </div>
            <div className="w-full flex flex-col gap-4">
              <div className="w-full">
                <select
                  value={selectedValue || ""}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="" disabled selected>
                    Parça seçin
                  </option>
                  {itemData.map((m, index) => (
                    <option key={index} value={m.id}>
                      {!m.name ? "isimlendirilmemiş parça" : m.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="w-full flex justify-end mt-4 gap-3">
              <button
                onClick={setVechileItemsHandler}
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                Ekle
              </button>
              <button
                onClick={onClose}
                className="bg-red-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                İptal
              </button>
            </div>
          </div>
        </Modal>
      </div>
    )
  );
};

export default AddVechileItemModal;
