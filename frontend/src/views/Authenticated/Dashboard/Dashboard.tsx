import { useEffect, useState } from "react";
import { getDashboardData } from "../../../services/dashboard";
import { useNavigate } from "react-router-dom";
import { GrFormNextLink } from "react-icons/gr";
interface PaymentStore {
  id: string;
  name: string;
  payment: number;
}

interface Payments {
  daily_total: {
    total: number;
    stores: PaymentStore[];
  };
  weekly_total: {
    total: number;
    stores: PaymentStore[];
  };
  monthly_total: {
    total: number;
    stores: PaymentStore[];
  };
}

function Dashboard() {
  const navigate = useNavigate();
  const [dashboardData, setDashboardData] = useState<Payments | null>(null);

  const getDashboardDataHandler = async () => {
    try {
      const response = await getDashboardData();
      if (response?.data) {
        setDashboardData(response.data.payments);
      }
    } catch (error) {
      console.error("Error fetching dashboard data: ", error);
    }
  };

  useEffect(() => {
    getDashboardDataHandler();
  }, []);

  return (
    <div className="bg-gradient-to-br  to-gray-200 p-6">
      <div className=" mx-auto">
        {/* Header */}
        <header className="text-center mb-6">
          <h1 className="text-3xl font-bold text-gray-800">Ödeme Verileri</h1>
          <p className="text-gray-500">
            Ödeme verileriniz buradan görebilirsiniz.
          </p>
        </header>

        {/* Payments Section */}
        {dashboardData ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Daily Payments */}
            <div className="bg-white shadow-lg rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">
                Günlük Ödemeler
              </h2>
              <p className="text-2xl font-bold text-blue-500 mb-4">
                Toplam: {dashboardData.daily_total.total} ₺
              </p>
              <div className="max-h-[500px] overflow-auto">
                {dashboardData.daily_total.stores && dashboardData.daily_total.stores
                  .filter(
                    (store) =>
                      store.id !== "00000000-0000-0000-0000-000000000000"
                  )
                  .map((store) => (
                    <div
                      key={store.id}
                      className="text-gray-700 w-full flex justify-between gap-2 border mb-1 p-2"
                    >
                      <div>{store.name}</div>
                      <div className="flex gap-2 items-center">
                        <div className="font-bold">{store.payment}₺</div>
                        <div>
                          <button
                            onClick={() => navigate(`/store/${store.id}`)}
                            className="p-1 bg-blue-200 text-blue-500 w-20 rounded-lg flex justify-center items-center"
                          >
                            <GrFormNextLink /> git
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>

            {/* Weekly Payments */}
            <div className="bg-white shadow-lg rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">
                Haftalık Ödemeler
              </h2>
              <p className="text-2xl font-bold text-green-500 mb-4">
                Toplam: {dashboardData.weekly_total.total} ₺
              </p>
              <div className="max-h-[500px] overflow-auto">
                {dashboardData.weekly_total.stores && dashboardData.weekly_total.stores
                  .filter(
                    (store) =>
                      store.id !== "00000000-0000-0000-0000-000000000000"
                  )
                  .map((store) => (
                    <div
                      key={store.id}
                      className="text-gray-700 w-full flex justify-between gap-2 border mb-1 p-2"
                    >
                      <div>{store.name}</div>
                      <div className="flex gap-2 items-center">
                        <div className="font-bold">{store.payment}₺</div>
                        <div>
                          <button
                            onClick={() => navigate(`/store/${store.id}`)}
                            className="p-1 bg-blue-200 text-blue-500 w-20 rounded-lg flex justify-center items-center"
                          >
                            <GrFormNextLink /> git
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>

            {/* Monthly Payments */}
            <div className="bg-white shadow-lg rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">
                Aylık Ödemeler
              </h2>
              <p className="text-2xl font-bold text-purple-500 mb-4">
                Toplam: {dashboardData.monthly_total.total} ₺
              </p>
              <div className="max-h-[500px] overflow-auto">
                {dashboardData.monthly_total.stores && dashboardData.monthly_total.stores
                  .filter(
                    (store) =>
                      store.id !== "00000000-0000-0000-0000-000000000000"
                  )
                  .map((store) => (
                    <div
                      key={store.id}
                      className="text-gray-700 w-full flex justify-between gap-2 border mb-1 p-2"
                    >
                      <div>{store.name}</div>
                      <div className="flex gap-2 items-center">
                        <div className="font-bold">{store.payment}₺</div>
                        <div>
                          <button
                            onClick={() => navigate(`/store/${store.id}`)}
                            className="p-1 bg-blue-200 text-blue-500 w-20 rounded-lg flex justify-center items-center"
                          >
                            <GrFormNextLink /> git
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center text-gray-500">Veriler Yükleniyor...</div>
        )}
      </div>
    </div>
  );
}

export default Dashboard;
