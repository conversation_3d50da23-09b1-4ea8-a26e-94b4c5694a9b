import { useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import {
  getVechileItemDataForStore,
  getVechileItemPaymentDataForStore,
  getSingleVechileDataForStore,
} from "../../../services/StoresServices/store";
import { IoIosArrowBack } from "react-icons/io";

interface ItemData {
  id: string;
  CreatedAt: string;
  UpdatedAt: string;
  DeletedAt: string | null;
  name: string;
  price: string;
  quantity: number;
  note: string;
  is_paid: number;
  vehicle_id: string;
}

// Define the type for payment data
interface PaymentData {
  id: string;
  amount: string;
  CreatedAt: string;
  payment_date: string;
  is_paid: number;
  note: string;
}

// Define the type for the API response
interface ApiResponse {
  page: number;
  per_page: number;
  total_pages: number;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
  rows: ItemData[];
}

interface SingleVechileData {
  CreatedAt: string; // ISO date string
  UpdatedAt: string; // ISO date string
  id: string; // UUID
  maked_by: string; // UUID
  paid_amount: number; // Paid amount (numeric)
  person_name: string; // Name of the person (string)
  phone: string; // Phone number (string, optional/nullable)
  plate_number: string; // Plate number (string, optional/nullable)
  remaining_amount: number; // Remaining amount (numeric)
  store_id: string; // Store ID (UUID)
  total_amount: number; // Total amount (numeric)
}

function StoreVechile() {
  const { id } = useParams<{ id: string }>();
  const [items, setItems] = useState<ItemData[]>([]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [singleVechileData, setSingleVechileData] =
    useState<SingleVechileData>();
  /*  const [totalAmount, setTotalAmount] = useState<number>(0);
  const [paidAmount, setPaidAmount] = useState<number>(0);
  const [remainingAmount, setRemainingAmount] = useState<number>(0); */
  const [paymentData, setPaymentData] = useState<PaymentData[]>([]);

  const getVechileItemDataForStoreHandler = async (page: number = 1) => {
    try {
      if (!id) return;
      setLoading(true);
      const response = await getVechileItemDataForStore(id, page);
      const data: ApiResponse = response.data;
      if (data) {
        setItems(data.rows);
        setCurrentPage(data.page);
        setTotalPages(data.total_pages);
        //  setTotalAmount(data.total_amount);
        // setPaidAmount(data.paid_amount);
        // setRemainingAmount(data.remaining_amount);
      }
    } catch (error) {
      console.error("Error fetching vehicle item data:", error);
    } finally {
      setLoading(false);
    }
  };

  const getVechileItemPaymentDataForStoreHandler = async () => {
    try {
      if (!id) return;
      const response = await getVechileItemPaymentDataForStore(id);
      if (response) {
        setPaymentData(response.data);
      }
    } catch (error) {
      console.error("Error fetching payment data:", error);
    }
  };

  const getSingleVechileDataForStoreHandler = async () => {
    try {
      if (!id) return;
      const response = await getSingleVechileDataForStore(id);
      if (response) {
        setSingleVechileData(response.data);
      }
    } catch (error) {
      console.error("Error fetching payment data:", error);
    }
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      getVechileItemDataForStoreHandler(newPage);
    }
  };

  useEffect(() => {
    getVechileItemDataForStoreHandler();
    getVechileItemPaymentDataForStoreHandler();
    getSingleVechileDataForStoreHandler();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-100 to-gray-200 p-6">
      <div className="max-w-6xl mx-auto bg-white shadow-lg rounded-lg p-6">
        <header className="mb-6">
          <button
            onClick={() => window.history.back()}
            className="flex items-center gap-2 text-gray-800 font-medium rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-400"
          >
            <IoIosArrowBack /> Geri Dön
          </button>
          <div className="w-full flex justify-between">
            <div className="text-2xl font-bold">Parçalar</div>
            <div className="text-2xl font-bold text-blue-700">
              {singleVechileData?.person_name}
            </div>
          </div>
          <p className="text-gray-600 mt-2">
            <strong>Toplam:</strong> {singleVechileData?.total_amount}₺ |{" "}
            <strong>Ödenen:</strong> {singleVechileData?.paid_amount}₺ |{" "}
            <strong>Kalan:</strong> {singleVechileData?.remaining_amount}₺
          </p>
        </header>

        {loading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin w-12 h-12 border-4 border-blue-400 border-t-transparent rounded-full"></div>
          </div>
        ) : (
          <div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
              {items.map((item) => (
                <div
                  key={item.id}
                  className="bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-lg shadow hover:shadow-lg transition p-4"
                >
                  <h2 className="text-lg font-semibold text-gray-700 mb-2">
                    {item.name}
                  </h2>
                  <p className="text-sm text-gray-600">
                    <strong>Fiyat:</strong> {item.price}₺
                  </p>
                  <p className="text-sm text-gray-600">
                    <strong>Miktar:</strong> {item.quantity}
                  </p>
                  <p className="text-sm text-gray-600">
                    <strong>Not:</strong> {item.note || "No notes"}
                  </p>
                  <p className="text-sm text-gray-400">
                    <strong>Oluşturulma Tarih:</strong>{" "}
                    {new Date(item.CreatedAt).toLocaleString()}
                  </p>
                </div>
              ))}
            </div>

            <div className="flex justify-between items-center mt-6">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`px-4 py-2 rounded-lg font-semibold shadow ${
                  currentPage === 1
                    ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                    : "bg-blue-600 text-white hover:bg-blue-700"
                }`}
              >
                Geri
              </button>
              <span className="text-gray-600 font-medium">
                Sayfa {currentPage}/{totalPages}
              </span>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`px-4 py-2 rounded-lg font-semibold shadow ${
                  currentPage === totalPages
                    ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                    : "bg-blue-600 text-white hover:bg-blue-700"
                }`}
              >
                İleri
              </button>
            </div>
          </div>
        )}
      </div>

      <div className="max-w-6xl mx-auto bg-white shadow-lg rounded-lg p-6 mt-6">
        <header className="mb-6">
          <h1 className="text-3xl font-extrabold text-gray-800">Ödemeler</h1>
        </header>

        <div>
          {paymentData?.map((payment) => (
            <div
              key={payment.id}
              className={`border border-gray-200 rounded-lg shadow p-4 mb-4 ${
                payment.is_paid === 1 ? "bg-green-100" : "bg-red-100"
              }`}
            >
              <p className="text-sm text-gray-700">
                <strong>Tutar:</strong> {payment.amount}₺
              </p>
              <p className="text-sm text-gray-700">
                <strong>Durum:</strong>{" "}
                {payment.is_paid === 1 ? "Ödendi" : "Ödenmedi"}
              </p>
              <p className="text-sm text-gray-600">
                <strong>Ödeme Tarihi:</strong>{" "}
                {payment.payment_date
                  ? new Date(payment.payment_date).toLocaleString()
                  : "Belirtilmemiş"}
              </p>
              <p className="text-sm text-gray-600">
                <strong>Not:</strong> {payment.note || "No notes"}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default StoreVechile;
