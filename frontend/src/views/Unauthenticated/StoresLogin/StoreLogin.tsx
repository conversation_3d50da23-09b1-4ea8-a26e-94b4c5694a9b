import React, { useEffect, useState } from "react";
import { login } from "../../../slices/storeSlice";
import { StoreLoginService } from "../../../services/authService";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { RootState } from "../../../app/store";
import { useSelector } from "react-redux";
import { useParams } from "react-router-dom";

const StoreLogin: React.FC = () => {
  const { name } = useParams();
  const store_token = useSelector((state: RootState) => state.store.token);
  const [username, setUsername] = useState<string>(name ? name : "");
  const [password, setPassword] = useState<string>("");

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    try {
      const response = await StoreLoginService(username, password);
      if (response.status === 200) {
        dispatch(login(response.data.Token));
      }
      navigate(`/stores/dashboard/${name}`);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (store_token) {
      navigate("store/dashboard");
    }
  }, []);

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="w-full max-w-md p-8 space-y-4 bg-white rounded-lg shadow-md">
        <h2
          onClick={() => navigate("/dashboard/", { replace: true })}
          className="text-2xl font-bold text-center text-gray-800"
        >
          Müşteri Giriş
        </h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label
              htmlFor="username"
              className="block text-sm font-medium text-gray-700"
            >
              Kullanıcı Adı
            </label>
            <input
              type="text"
              id="username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="block w-full px-4 py-2 mt-1 border rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300"
              placeholder="Kullanıcı Adınızı Girin"
              required
            />
          </div>
          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium text-gray-700"
            >
              Şifre
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="block w-full px-4 py-2 mt-1 border rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm border-gray-300"
              placeholder="Şifrenizi Girin"
              required
            />
          </div>
          <button
            type="submit"
            className="w-full px-4 py-2 text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Enter
          </button>
        </form>
      </div>
    </div>
  );
};

export default StoreLogin;
