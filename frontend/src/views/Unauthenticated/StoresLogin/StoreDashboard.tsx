import { useEffect, useState, useCallback } from "react";
import { getVechileDataForStore } from "../../../services/StoresServices/store";
import { useParams } from "react-router-dom";
import { FaArrowRightFromBracket } from "react-icons/fa6";
import { FaSearch } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import debounce from "lodash.debounce";
// Define the type for the vehicle data
interface VehicleData {
  id: string;
  plate_number: string;
  person_name: string;
  phone: string;
  items: string[];
  CreatedAt: string;
  remaining: number;
  total_payment: number;
}

// Define the type for the API response structure
interface ApiResponse {
  page: number;
  per_page: number;
  total_pages: number;
  total_amount: number;
  remaining_amount: number;
  total: number;
  rows: VehicleData[];
  slug_name: string;
}

function StoreDashboard() {
  const navigate = useNavigate();

  const { name } = useParams<{ name: string }>();
  const [slugName, setSlugName] = useState<string>("");
  const [vehicleData, setVehicleData] = useState<VehicleData[]>([]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [payDetail, setPayDetail] = useState({
    remaining_amount: 0,
    total_amount: 0,
    total: 0,
  });

  const getVechileDataForStoreHandler = async (page: number = 1, key: string = searchTerm) => {
    try {
      if (!name) {
        return;
      }

      setLoading(true);
      const response = await getVechileDataForStore(name, page, key);

      // Check if response exists
      if (!response) {
        console.error("No response from API");
        setVehicleData([]);
        return;
      }

      const data: ApiResponse = response.data;

      if (data) {
        setSlugName(data.slug_name || "");
        setVehicleData(data.rows || []);
        setCurrentPage(data.page || 1);
        setTotalPages(data.total_pages || 1);
        setPayDetail({
          remaining_amount: data.remaining_amount || 0,
          total: data.total || 0,
          total_amount: data.total_amount || 0,
        });
      } else {
        // Handle empty data
        setVehicleData([]);
        setTotalPages(1);
        setCurrentPage(1);
      }
    } catch (error) {
      console.error("Error fetching vehicle data:", error);
      // Set empty data on error
      setVehicleData([]);
      setTotalPages(1);
      setCurrentPage(1);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      getVechileDataForStoreHandler(newPage);
    }
  };

  // Debounce search to avoid too many API calls
  const debouncedSearch = useCallback(
    debounce((searchValue: string) => {
      getVechileDataForStoreHandler(1, searchValue);
    }, 500),
    []
  );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    debouncedSearch(value);
  };

  useEffect(() => {
    getVechileDataForStoreHandler();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50  to-indigo-100 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="sticky top-0 bg-white z-10 p-2">
          <div className="w-full text-center text-2xl font-bold mb-2">
            {slugName}
          </div>

          <div className="w-full flex justify-center mb-2 gap-1">
            <div className="bg-orange-100 border border-orange-500 text-orange-500 p-1 rounded-md flex justify-center items-center w-1/3">
              Ödenen:{" "}
              {Number(
                payDetail.total_amount - payDetail.remaining_amount
              ).toLocaleString("TR-tr")}
              ₺
            </div>
            <div className="bg-red-100 border border-red-500 text-red-500 p-1 flex rounded-md justify-center items-center w-1/3">
              Kalan:{" "}
              {Number(payDetail.remaining_amount).toLocaleString("TR-tr")}₺
            </div>
            <div className="bg-green-100 border border-green-500 text-green-500 p-1 rounded-md flex justify-center items-center w-1/3">
              Toplam: {Number(payDetail.total_amount).toLocaleString("TR-tr")}₺
            </div>
          </div>

          {/* Search Input */}
          <div className="relative mt-3 mb-2">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <FaSearch className="w-4 h-4 text-gray-500" />
            </div>
            <input
              type="text"
              className="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Araç, plaka veya telefon ara..."
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </div>
        </div>

        {/* Content */}
        {loading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin w-12 h-12 border-4 border-blue-400 border-t-transparent rounded-full"></div>
          </div>
        ) : (
          <div className="bg-white shadow-lg rounded-lg p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
              {vehicleData.map((vehicle) => (
                <div
                  key={vehicle.id}
                  className={`rounded-lg shadow hover:shadow-lg transition p-4 ${
                    vehicle.remaining === 0
                      ? "bg-green-200"
                      : "bg-gray-50"
                  }`}
                >
                  <div className="w-full flex justify-between">
                    <h2 className="text-lg font-semibold text-indigo-700 mb-2">
                      {vehicle.person_name}
                    </h2>
                    <div className="flex gap-2">
                      <div className="text-sm ">
                        Kalan: {vehicle.remaining}₺
                      </div>
                      <div className="text-sm">
                        Toplam: {vehicle.total_payment}₺
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 mb-4">
                    <span className="font-medium">Telefon:</span>{" "}
                    {vehicle.phone}
                  </p>
                  <p className="text-sm text-gray-600 mb-4">
                    <span className="font-medium">Parçalar:</span>{" "}
                    {vehicle && vehicle.items
                      ? vehicle.items.length > 18
                        ? vehicle.items.slice(0, 18).join(", ") + "..."
                        : vehicle.items.join(", ")
                      : "Parça Yok"}
                  </p>
                  <div className="w-full flex justify-between">
                    <p className="text-sm text-gray-400">
                      Oluşturulma Tarihi: {new Date(vehicle.CreatedAt).toLocaleString()}
                    </p>
                    <button
                      className="text-white text-sm p-1 flex justify-center items-center gap-1 border bg-blue-400 rounded-2xl"
                      onClick={() => navigate(`/stores/vechile/${vehicle.id}`)}
                    >
                      <span className="hidden md:block">Detaylar</span>
                      <FaArrowRightFromBracket />
                    </button>
                  </div>
                </div>
              ))}

              {vehicleData.length === 0 && (
                <div className="col-span-2 text-center p-10 bg-gray-50 rounded-lg">
                  <p className="text-gray-500 text-lg">
                    Hiç araç bulunamadı.
                  </p>
                </div>
              )}
            </div>

            {/* Pagination */}
            <div className="flex justify-between items-center mt-6">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className={`px-4 py-2 rounded-lg font-semibold shadow ${
                  currentPage === 1
                    ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                    : "bg-indigo-600 text-white hover:bg-indigo-700"
                }`}
              >
                Geri
              </button>
              <span className="text-gray-700 font-medium">
                Sayfa {currentPage}/{totalPages}
              </span>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`px-4 py-2 rounded-lg font-semibold shadow ${
                  currentPage === totalPages
                    ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                    : "bg-indigo-600 text-white hover:bg-indigo-700"
                }`}
              >
                İleri
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default StoreDashboard;
