import { configureStore } from "@reduxjs/toolkit";
import storeSlice from "../slices/storeSlice";
import userReducer from "../slices/userSlice";
import { TypedUseSelectorHook, useDispatch, useSelector } from "react-redux";
export const store = configureStore({
  reducer: {
    user: userReducer,
    store: storeSlice,
  },
});

// RootState ve AppDispatch türlerini çıkarın
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export const useAppDispatch = () => useDispatch<AppDispatch>(); // Correctly typed dispatch
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector; // Correctly typed selector
