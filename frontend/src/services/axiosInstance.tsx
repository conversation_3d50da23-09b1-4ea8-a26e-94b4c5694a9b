import axios from "axios";

export const logoutUser = () => {
  localStorage.removeItem("token");
  window.location.href = "/auth/login";
};

const apiClient = axios.create({
  baseURL: "/api/v1",
  timeout: 60000,
  withCredentials: true,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add token to every request
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
apiClient.interceptors.response.use(
  (response: any): any => {
    return response;
  },
  (error) => {
    console.log("axios error", error);
    if (error.response?.status === 401) {
      logoutUser();
    }
    return Promise.reject(error);
  }
);

export default apiClient;
