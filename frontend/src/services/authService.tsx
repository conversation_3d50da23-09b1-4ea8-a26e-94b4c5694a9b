import apiClient from "./axiosInstance";
import storeApiClient from "./storeAxiosInstance";

export const LoginService = async (username: string, password: string) => {
  try {
    const response = await apiClient.post(
      "/login",
      JSON.stringify({ username, password })
    );
    if (response?.status === 200) {
      return response.data;
    } else {
      throw new Error("Login failed");
    }
  } catch (error) {
    throw error;
  }
};

export const StoreLoginService = async (username: string, password: string) => {
  try {
    const response = await storeApiClient.post(
      "/store-login",
      JSON.stringify({ username, password })
    );
    if (response?.status === 200) {
      return response.data;
    } else {
      throw new Error("<PERSON><PERSON> failed");
    }
  } catch (error) {
    throw error;
  }
};
