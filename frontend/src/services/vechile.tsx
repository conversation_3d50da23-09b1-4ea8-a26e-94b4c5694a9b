import { UpdateVehicle } from "../types/vechile";
import apiClient from "./axiosInstance";

export const getStoreVechile = async (
  storeId: string,
  page: number = 1,
  per_page: number = 10,
  key?: string
) => {
  try {
    const response = await apiClient.get(
      `/vehicle/store/${storeId}?page=${page}&per_page=${per_page}&key=${key}`
    );
    if (response?.status === 200) {
      return response.data; // Return the `data` part of the response
    } else {
      throw new Error("Failed to fetch vehicle data");
    }
  } catch (error) {
    console.error("Error fetching vehicle data:", error);
    throw error;
  }
};

export const setVechile = async (formData: any) => {
  try {
    const response = await apiClient.post(`/vehicle`, formData);
    if (response?.status === 201) {
      return response.data;
    } else {
      throw new Error("Failed to fetch vehicle data");
    }
  } catch (error) {
    console.error("Error fetching vehicle data:", error);
    throw error;
  }
};

export const updateVechileService = async (
  id: string,
  formData: UpdateVehicle
) => {
  try {
    const response = await apiClient.put(`/vehicle/${id}`, formData);
    if (response?.status === 200) {
      return response.data;
    } else {
      throw new Error("Failed to update vehicle data");
    }
  } catch (error) {
    console.error("Error update vehicle data:", error);
    throw error;
  }
};

export const deleteVechile = async (id: string) => {
  try {
    const response = await apiClient.delete(`/vehicle/${id}`);
    if (response?.status === 200) {
      return response.data;
    } else {
      throw new Error("Failed to delete vehicle data");
    }
  } catch (error) {
    console.error("Error delete vehicle data:", error);
    throw error;
  }
};

export const getSingleVechile = async (id: string) => {
  try {
    const response = await apiClient.get(`/vehicle/${id}`);
    if (response?.status === 200) {
      return response.data;
    } else {
      throw new Error("Failed to delete vehicle data");
    }
  } catch (error) {
    console.error("Error delete vehicle data:", error);
    throw error;
  }
};

export const getVechilePayments = async (id: string) => {
  try {
    const response = await apiClient.get(`/payment/vehicle/${id}`);
    if (response?.status === 200) {
      return response.data;
    } else {
      throw new Error("Failed to fetch vehicle data");
    }
  } catch (error) {
    console.error("Error fetch vehicle data:", error);
    throw error;
  }
};
