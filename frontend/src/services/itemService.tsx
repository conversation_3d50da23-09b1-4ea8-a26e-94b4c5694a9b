import apiClient from "./axiosInstance";
import { CreateItem } from "../types/item";
import { CreateVehicleItem, UpdateVehicleItem } from "../types/vechile";

export const getItem = async (
  page: number = 1,
  perPage: number = 10,
  key?: string
) => {
  try {
    const response = await apiClient.get(`/item`, {
      params: { page, per_page: perPage, key },
    });
    if (response?.status === 200) {
      return response.data;
    } else {
      throw new Error("Failed to fetch store data");
    }
  } catch (error) {
    console.error("Error fetching store data:", error);
    throw error;
  }
};

export const setItem = async (values: CreateItem) => {
  try {
    const response = await apiClient.post(`/item`, values);
    if (response?.status === 201) {
      return response.data;
    } else {
      throw new Error("Failed to fetch store data");
    }
  } catch (error) {
    console.error("Error fetching store data:", error);
    throw error;
  }
};
export const updateItem = async (id: string, values: CreateItem) => {
  try {
    const response = await apiClient.put(`/item/${id}`, values);
    if (response?.status === 200) {
      return response.data;
    } else {
      throw new Error("Failed to updateItem");
    }
  } catch (error) {
    console.error("Error updateItem:", error);
    throw error;
  }
};
export const deleteItem = async (id: string) => {
  try {
    const response = await apiClient.delete(`/item/${id}`);
    if (response?.status === 200) {
      return response.data;
    } else {
      throw new Error("Failed to delete");
    }
  } catch (error) {
    console.error("Error delete:", error);
    throw error;
  }
};

export const getVechileItems = async (
  id: string,
  page: number = 1,
  perPage: number = 10,
  key?: string
) => {
  try {
    const response = await apiClient.get(
      `/vehicle-item?page=${page}&per_page=${perPage}&vehicle_id=${id}&key=${key}`
    );
    if (response?.status === 200) {
      return response.data;
    } else {
      throw new Error("Failed to fetch store data");
    }
  } catch (error) {
    console.error("Error fetching store data:", error);
    throw error;
  }
};

export const setVechileItems = async (formData: CreateVehicleItem) => {
  try {
    const response = await apiClient.post(`/vehicle-item`, formData);
    if (response?.status === 201) {
      return response.data;
    } else {
      throw new Error("Failed to fetch store data");
    }
  } catch (error) {
    console.error("Error fetching store data:", error);
    throw error;
  }
};

export const updateSingleVechileItem = async (
  id: string,
  UpdateVehicleItem: UpdateVehicleItem
) => {
  try {
    const response = await apiClient.put(
      `/vehicle-item/${id}`,
      UpdateVehicleItem
    );
    if (response?.status === 200) {
      return response.data;
    } else {
      throw new Error("Failed to update vehicle data");
    }
  } catch (error) {
    console.error("Error update vehicle data:", error);
    throw error;
  }
};

export const deleteSingleVechileItem = async (id: string) => {
  try {
    const response = await apiClient.delete(`/vehicle-item/${id}`);
    if (response?.status === 200) {
      return response.data;
    } else {
      throw new Error("Failed to delete vehicle data");
    }
  } catch (error) {
    console.error("Error delete vehicle data:", error);
    throw error;
  }
};
