import apiClient from "./axiosInstance";
import { CreateStore } from "../types/store";

export const getStore = async (
  page: number = 1,
  perPage: number = 10,
  key: string
) => {
  try {
    const response = await apiClient.get(`/store`, {
      params: { page, per_page: perPage, key }, // Query parameters
    });
    if (response?.status === 200) {
      return response.data;
    } else {
      throw new Error("Failed to fetch store data");
    }
  } catch (error) {
    console.error("Error fetching store data:", error);
    throw error;
  }
};

export const setStore = async (form: CreateStore) => {
  try {
    const response = await apiClient.post(`/store`, form);
    if (response?.status === 201) {
      return response.data;
    } else {
      throw new Error("Failed to set store data");
    }
  } catch (error) {
    console.error("Error set store data:", error);
    throw error;
  }
};

export const deleteStore = async (id: string) => {
  try {
    const response = await apiClient.delete(`/store/${id}`);
    if (response?.status === 200) {
      return response.data;
    } else {
      throw new Error("Failed to set store data");
    }
  } catch (error) {
    console.error("Error set store data:", error);
    throw error;
  }
};

export const updateStore = async (id: string, formData: any) => {
  try {
    const response = await apiClient.put(`/store/${id}`, formData);
    if (response?.status === 200) {
      return response.data;
    } else {
      throw new Error("Failed to set store data");
    }
  } catch (error) {
    console.error("Error set store data:", error);
    throw error;
  }
};

export const getSingleStore = async (id: string) => {
  try {
    const response = await apiClient.get(`/store/${id}`);
    if (response?.status === 200) {
      return response.data;
    } else {
      throw new Error("Failed to fetch store data");
    }
  } catch (error) {
    console.error("Error fetching store data:", error);
    throw error;
  }
};
