import { CreatePayment, UpdatePayment } from "../types/payment";
import apiClient from "./axiosInstance";

export const createPayment = async (form: CreatePayment) => {
  try {
    const response = await apiClient.post(`/payment`, form);
    if (response?.status === 201) {
      return response.data;
    } else {
      throw new Error("Failed to set store data");
    }
  } catch (error) {
    console.error("Error set store data:", error);
    throw error;
  }
};

export const updatePayment = async (id: string, form: UpdatePayment) => {
  try {
    const response = await apiClient.put(`/payment/${id}`, form);
    if (response?.status === 200) {
      return response.data;
    } else {
      throw new Error("Failed to delete item data");
    }
  } catch (error) {
    console.error("Error delete item data:", error);
    throw error;
  }
};

export const deletePayment = async (id: string) => {
  try {
    const response = await apiClient.delete(`/payment/${id}`);
    if (response?.status === 200) {
      return response.data;
    } else {
      throw new Error("Failed to delete item data");
    }
  } catch (error) {
    console.error("Error delete item data:", error);
    throw error;
  }
};
