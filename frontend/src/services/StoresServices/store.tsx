import storeApiClient from "../storeAxiosInstance";
import apiClient from "../axiosInstance";

// http://localhost:8000/api/v1/vehicle/slug/test
export const getVechileDataForStore = async (id: string, page: number, key: string = "") => {
  try {
    const response = await storeApiClient.get(
      `vehicle/slug/${id}?page=${page}&per_page=${10}&key=${key}`
    );

    if (response.status === 200) {
      return response.data;
    }
    return { data: { rows: [], page: 1, total_pages: 1, total: 0, remaining_amount: 0, total_amount: 0 } };
  } catch (error) {
    console.log(error);
    return { data: { rows: [], page: 1, total_pages: 1, total: 0, remaining_amount: 0, total_amount: 0 } };
  }
};

// Get stores that have deleted vehicles
export const getStoresWithDeletedVehicles = async (key: string = "") => {
  try {
    const response = await apiClient.get(`/vehicle/trash/stores?key=${key}`);
    if (response?.status === 200) {
      return response.data;
    }
    return { data: [] };
  } catch (error) {
    console.error("Error fetching stores with deleted vehicles:", error);
    throw error;
  }
};

// Get deleted vehicles for a specific store
export const getDeletedVehiclesByStoreId = async (storeId: string, page: number = 1, key: string = "") => {
  try {
    const response = await apiClient.get(
      `/vehicle/trash/store/${storeId}?page=${page}&per_page=10&key=${key}`
    );
    if (response?.status === 200) {
      return response.data;
    }
    return { data: { rows: [], page: 1, total_pages: 1, total: 0 } };
  } catch (error) {
    console.error("Error fetching deleted vehicles:", error);
    throw error;
  }
};

// Restore a deleted vehicle
export const restoreVehicle = async (id: string) => {
  try {
    const response = await apiClient.put(`/vehicle/restore/${id}`);
    if (response?.status === 200) {
      return response.data;
    }
    throw new Error("Failed to restore vehicle");
  } catch (error) {
    console.error("Error restoring vehicle:", error);
    throw error;
  }
};



// http://localhost:8000/api/v1/vehicle-item/outside/ac383cab-142c-41d3-ad43-64e33e8d6893

export const getVechileItemDataForStore = async (id: string, page: number) => {
  try {
    const response = await storeApiClient.get(
      `vehicle-item/outside/${id}?page=${page}&per_page=${10}`
    );

    if (response.status === 200) {
      return response.data;
    }
    return { data: { rows: [] } };
  } catch (error) {
    console.log(error);
    return { data: { rows: [] } };
  }
};

// http://localhost:8000/api/v1/payment/outside/ac383cab-142c-41d3-ad43-64e33e8d6893

export const getVechileItemPaymentDataForStore = async (id: string) => {
  try {
    const response = await storeApiClient.get(`payment/outside/${id}`);

    if (response.status === 200) {
      return response.data;
    }
    return { data: { rows: [] } };
  } catch (error) {
    console.log(error);
    return { data: { rows: [] } };
  }
};
// http://localhost:8000/api/v1/vehicle/outside/ac383cab-142c-41d3-ad43-64e33e8d6893

export const getSingleVechileDataForStore = async (id: string) => {
  try {
    const response = await storeApiClient.get(`vehicle/outside/${id}`);

    if (response.status === 200) {
      return response.data;
    }
    return { data: {} };
  } catch (error) {
    console.log(error);
    return { data: {} };
  }
};
