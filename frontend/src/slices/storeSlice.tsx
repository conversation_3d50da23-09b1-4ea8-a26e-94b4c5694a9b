import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface StoreState {
  token: string | null;
}

const initialState: StoreState = {
  token: sessionStorage.getItem("store-token"), // sessionStorage'dan ba<PERSON>
};

const storeSlice = createSlice({
  name: "store",
  initialState,
  reducers: {
    login(state, action: PayloadAction<string>) {
      state.token = action.payload;
      sessionStorage.setItem("store-token", action.payload); // sessionStorage'a kaydet
    },
    logout(state) {
      state.token = null;
      sessionStorage.removeItem("store-token"); // sessionStorage'dan sil
    },
  },
});

export const { login, logout } = storeSlice.actions;
export default storeSlice.reducer;
