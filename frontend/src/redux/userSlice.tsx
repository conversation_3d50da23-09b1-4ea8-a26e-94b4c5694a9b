import { createSlice } from "@reduxjs/toolkit";

export const userSlice = createSlice({
  name: "user",
  initialState: {
    IsSucceeded: false,
    token: null,
  },

  reducers: {
    login: (state, action) => {
      console.log("🚀 ~ action:", action);
      console.log("🚀 ~ state:", state);
      state.IsSucceeded = true;
      localStorage.setItem("token", action.payload.token);
    },
    logout: (state) => {
      state.IsSucceeded = false;
      localStorage.removeItem("token");
    },
  },
});

export const { login, logout } = userSlice.actions;
