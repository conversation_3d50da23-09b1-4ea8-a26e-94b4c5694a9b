{"name": "vv", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@heroicons/react": "^2.1.5", "@mui/material": "^6.1.10", "@reduxjs/toolkit": "^2.3.0", "axios": "^1.7.7", "date-fns": "^4.1.0", "lodash.debounce": "^4.0.8", "moment": "^2.30.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-redux": "^9.1.2", "react-router-dom": "^6.28.0", "react-select": "^5.8.3", "react-to-print": "^3.0.2", "react-toastify": "^10.0.6"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/node": "^22.9.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.15", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^5.4.10"}}