package routes

import (
	"strconv"

	"github.com/SametAvcii/kalkan-auto/pkg/consts"
	"github.com/SametAvcii/kalkan-auto/pkg/domains/store"
	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/entities"
	"github.com/SametAvcii/kalkan-auto/pkg/log"
	"github.com/SametAvcii/kalkan-auto/pkg/middleware"
	"github.com/gin-gonic/gin"
)

func StoreRoutes(r *gin.RouterGroup, s store.Service) {
	r.GET("/store", middleware.Authorized(), GetStores(s))
	r.POST("/store", middleware.Authorized(), CreateStore(s))
	r.GET("/store/:id", middleware.Authorized(), GetStoreById(s))
	r.PUT("/store/:id", middleware.Authorized(), UpdateStore(s))
	r.DELETE("/store/:id", middleware.Authorized(), DeleteStore(s))
	r.GET("/store/set-slug-name", middleware.Authorized(), SetSlugName(s))
}

func GetStores(s store.Service) func(c *gin.Context) {
	return func(c *gin.Context) {

		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}

		key := c.Query("key")

		stores, err := s.GetStores(c, page, perPage, key)
		if err != nil {

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   stores,
			"status": 200,
		})
	}
}

func CreateStore(s store.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CreateStore
		if err := c.ShouldBindJSON(&req); err != nil {

			log.CreateLog(&entities.Log{
				Title:   "Create Store Failed",
				Message: "Create Store Failed, bind json err:" + err.Error(),
				Entity:  "store",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  consts.StoreCreateFailed,
				"status": 400,
			})
			return
		}

		err := s.CreateStore(c, req)
		if err != nil {

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   consts.StoreCreateSuccess,
			"status": 201,
		})
	}
}

func GetStoreById(s store.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		store, err := s.GetStoreById(c, id)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   store,
			"status": 200,
		})
	}
}

func UpdateStore(s store.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		var req dtos.UpdateStore
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Update Store Failed",
				Message: "Update Store Failed, bind json err:" + err.Error(),
				Entity:  "store",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  consts.StoreUpdateFailed,
				"status": 400,
			})
			return
		}

		err := s.UpdateStore(c, id, req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   consts.StoreUpdateSuccess,
			"status": 200,
		})
	}
}

func DeleteStore(s store.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")

		err := s.DeleteStore(c, id)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   consts.StoreDeleteSuccess,
			"status": 200,
		})
	}
}

func SetSlugName(s store.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		s.SetSlugName(c)
		c.JSON(200, gin.H{
			"data":   consts.StoreSlugNameSetSuccess,
			"status": 200,
		})
	}
}
