package routes

import (
	"github.com/SametAvcii/kalkan-auto/pkg/consts"
	"github.com/SametAvcii/kalkan-auto/pkg/domains/auth"
	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/entities"
	"github.com/SametAvcii/kalkan-auto/pkg/log"
	"github.com/gin-gonic/gin"
)

func AuthRoutes(r *gin.RouterGroup, s auth.Service) {
	r.POST("/login", Login(s))
	r.POST("/user", CreateUser(s))
	r.POST("/store-login", StoreLogin(s))

}

func Login(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {

		var req dtos.AuthenticationRequest
		if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {

			log.CreateLog(&entities.Log{
				Title:   "Login Failed",
				Message: "Login Failed, bind json err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.Login(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func CreateUser(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {

		var req dtos.CreateUserReqDto
		if err := c.ShouldBindJSON(&req); err != nil {

			log.CreateLog(&entities.Log{
				Title:   "Login Failed",
				Message: "Login Failed, bind json err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		err := s.CreateUser(req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		c.JSON(200, gin.H{
			"data":   consts.CreateUserSuccess,
			"status": 200,
		})
	}
}

func StoreLogin(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {

		var req dtos.AuthenticationRequest
		if err := c.ShouldBindJSON(&req); err != nil {

			log.CreateLog(&entities.Log{
				Title:   "Login Failed",
				Message: "Login Failed, bind json err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.StoreLogin(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}
