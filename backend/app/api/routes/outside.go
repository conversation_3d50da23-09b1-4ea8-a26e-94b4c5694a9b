package routes

import (
	"strconv"

	"github.com/SametAvcii/kalkan-auto/pkg/domains/item"
	"github.com/SametAvcii/kalkan-auto/pkg/domains/payment"
	"github.com/SametAvcii/kalkan-auto/pkg/domains/vehicle"
	"github.com/SametAvcii/kalkan-auto/pkg/middleware"
	"github.com/SametAvcii/kalkan-auto/pkg/state"
	"github.com/gin-gonic/gin"
)

func OutsideRoutes(r *gin.RouterGroup, s vehicle.Service, p payment.Service, i item.Service) {

	r.GET("/vehicle/slug/:slug_name", middleware.StoreAuthorized(), GetVehiclesBySlugName(s))
	r.GET("/vehicle/outside/:id", middleware.StoreAuthorized(), GetVehicleByIdOutside(s))
	r.GET("/payment/outside/:vehicle_id", middleware.StoreAuthorized(), GetPaymentsByVehicleIdOutside(p))
	r.GET("/vehicle-item/outside/:vehicle_id", middleware.StoreAuthorized(), GetVehicleItemByVehicleIdOutside(i))

}

func GetVehiclesBySlugName(s vehicle.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		slugName := state.GetCurrentSlugName(c)

		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}
		key := c.Query("key")

		item, err := s.GetVehiclesBySlugName(c, slugName, page, perPage, key)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":      item,
			"slug_name": slugName,
			"status":    200,
		})
	}
}

func GetVehicleByIdOutside(s vehicle.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		vehicle, err := s.GetVehicleById(c, id)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   vehicle,
			"status": 200,
		})
	}
}

func GetPaymentsByVehicleIdOutside(s payment.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		vehicleId := c.Param("vehicle_id")

		item, err := s.GetPaymentsByVehicleId(c, vehicleId)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   item,
			"status": 200,
		})
	}
}

func GetVehicleItemByVehicleIdOutside(s item.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		vehicleId := c.Param("vehicle_id")

		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}
		key := c.Query("key")

		item, err := s.GetVehicleItems(c, page, perPage, vehicleId, key)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   item,
			"status": 200,
		})
	}
}
