package routes

import (
	"github.com/SametAvcii/kalkan-auto/pkg/domains/item"
	"github.com/SametAvcii/kalkan-auto/pkg/domains/payment"
	"github.com/gin-gonic/gin"
)

func DashboardRoutes(r *gin.RouterGroup, s payment.Service, i item.Service) {
	r.GET("/dashboard", GetDashboard(s, i))
}

func GetDashboard(s payment.Service, i item.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		resp, err := s.GetPaymentsDashboard(c, 1, 10)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		
		c.JSON(200, gin.H{
			"data":   gin.H{"payments": resp, "items": "resp2"},
			"status": 200,
		})
	}

}
