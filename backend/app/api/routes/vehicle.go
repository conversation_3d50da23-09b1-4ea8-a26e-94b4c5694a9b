package routes

import (
	"strconv"

	"github.com/SametAvcii/kalkan-auto/pkg/consts"
	"github.com/SametAvcii/kalkan-auto/pkg/domains/vehicle"
	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/entities"
	"github.com/SametAvcii/kalkan-auto/pkg/log"
	"github.com/SametAvcii/kalkan-auto/pkg/middleware"
	"github.com/gin-gonic/gin"
)

func VehicleRoutes(r *gin.RouterGroup, s vehicle.Service) {
	r.GET("/vehicle", middleware.Authorized(), GetVehicle(s))
	r.POST("/vehicle", middleware.Authorized(), CreateVehicle(s))
	r.GET("/vehicle/:id", middleware.Authorized(), GetVehicleById(s))
	r.PUT("/vehicle/:id", middleware.Authorized(), UpdateVehicle(s))
	r.DELETE("/vehicle/:id", middleware.Authorized(), DeleteVehicle(s))
	r.PUT("/vehicle/restore/:id", middleware.Authorized(), RestoreVehicle(s))
	r.GET("/vehicle/store/:store_id", middleware.Authorized(), GetVehiclesByStoreId(s))
	r.GET("/vehicle/trash/store/:store_id", middleware.Authorized(), GetDeletedVehiclesByStoreId(s))
	r.GET("/vehicle/trash/stores", middleware.Authorized(), GetStoresWithDeletedVehicles(s))

}

func GetVehicle(s vehicle.Service) func(c *gin.Context) {
	return func(c *gin.Context) {

		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}

		vehicle, err := s.GetVehicles(c, page, perPage)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Vehicle Failed",
				Message: "Get Vehicle Failed, err:" + err.Error(),
				Entity:  "vehicle",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   vehicle,
			"status": 200,
		})
	}
}

func CreateVehicle(s vehicle.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CreateVehicle
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Create Vehicle Failed",
				Message: "Create Vehicle Failed, bind json err:" + err.Error(),
				Entity:  "vehicle",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  consts.VehicleCreateFailed,
				"status": 400,
			})
			return
		}

		err := s.CreateVehicle(c, req)
		if err != nil {

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   consts.VehicleCreateSuccess,
			"status": 201,
		})
	}
}

func GetVehicleById(s vehicle.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		vehicle, err := s.GetVehicleById(c, id)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   vehicle,
			"status": 200,
		})
	}
}

func UpdateVehicle(s vehicle.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		var req dtos.UpdateVehicle
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Update Vehicle Failed",
				Message: "Update Vehicle Failed, bind json err:" + err.Error(),
				Entity:  "vehicle",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  consts.VehicleUpdateFailed,
				"status": 400,
			})
			return
		}

		err := s.UpdateVehicle(c, id, req)
		if err != nil {

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   consts.VehicleUpdateSuccess,
			"status": 200,
		})
	}
}

func DeleteVehicle(s vehicle.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")

		err := s.DeleteVehicle(c, id)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   consts.VehicleDeleteSuccess,
			"status": 200,
		})
	}
}

func GetVehiclesByStoreId(s vehicle.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		storeId := c.Param("store_id")

		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}
		key := c.Query("key")

		item, err := s.GetVehiclesByStoreId(c, storeId, page, perPage, key)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   item,
			"status": 200,
		})
	}
}

func GetDeletedVehiclesByStoreId(s vehicle.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		storeId := c.Param("store_id")

		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}
		key := c.Query("key")

		item, err := s.GetDeletedVehiclesByStoreId(c, storeId, page, perPage, key)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   item,
			"status": 200,
		})
	}
}

func RestoreVehicle(s vehicle.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")

		err := s.RestoreVehicle(c, id)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   "Vehicle restored successfully",
			"status": 200,
		})
	}
}

func GetStoresWithDeletedVehicles(s vehicle.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get search key from query parameters
		key := c.Query("key")

		stores, err := s.GetStoresWithDeletedVehicles(c, key)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   stores,
			"status": 200,
		})
	}
}
