package routes

import (
	"strconv"

	"github.com/SametAvcii/kalkan-auto/pkg/consts"
	"github.com/SametAvcii/kalkan-auto/pkg/domains/item"
	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/entities"
	"github.com/SametAvcii/kalkan-auto/pkg/log"
	"github.com/SametAvcii/kalkan-auto/pkg/middleware"
	"github.com/gin-gonic/gin"
)

func ItemRoutes(r *gin.RouterGroup, s item.Service) {
	r.GET("/item", middleware.Authorized(), GetItem(s))
	r.POST("/item", middleware.Authorized(), CreateItem(s))
	r.GET("/vehicle-item", middleware.Authorized(), GetVehicleItem(s))
	r.POST("/vehicle-item", middleware.Authorized(), CreateVehicleItem(s))
	r.GET("/item/:id", middleware.Authorized(), GetItemById(s))
	r.GET("/vehicle-item/:id", middleware.Authorized(), GetVehicleItemById(s))
	r.PUT("/item/:id", middleware.Authorized(), UpdateItem(s))
	r.PUT("/vehicle-item/:id", middleware.Authorized(), UpdateVehicleItem(s))
	r.GET("/vehicle-item/vehicle/:vehicle_id", middleware.Authorized(), GetVehicleItemByVehicleId(s))
	r.DELETE("/item/:id", middleware.Authorized(), DeleteItem(s))
	r.DELETE("/vehicle-item/:id", middleware.Authorized(), DeleteVehicleItem(s))
}

func GetItem(s item.Service) func(c *gin.Context) {
	return func(c *gin.Context) {

		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}

		key := c.Query("key")

		item, err := s.GetItems(c, key, page, perPage)
		if err != nil {

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   item,
			"status": 200,
		})
	}
}

func CreateItem(s item.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CreateItem
		if err := c.ShouldBindJSON(&req); err != nil {

			log.CreateLog(&entities.Log{
				Title:   "Create Item  Failed",
				Message: "Create Item  Failed, bind json err:" + err.Error(),
				Entity:  "item",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  consts.ItemCreateFailed,
				"status": 400,
			})
			return
		}

		err := s.CreateItem(c, req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   consts.ItemCreateSuccess,
			"status": 201,
		})
	}
}

func GetVehicleItem(s item.Service) func(c *gin.Context) {
	return func(c *gin.Context) {

		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}
		key := c.Query("key")

		vehicleId := c.Query("vehicle_id")

		item, err := s.GetVehicleItems(c, page, perPage, vehicleId, key)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   item,
			"status": 200,
		})
	}
}

func CreateVehicleItem(s item.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CreateVehicleItem
		if err := c.ShouldBindJSON(&req); err != nil {

			log.CreateLog(&entities.Log{
				Title:   "Create Vehicle Item Failed",
				Message: "Create Vehicle Item Failed, bind json err:" + err.Error(),
				Entity:  "vehicle_item",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  consts.VehicleItemCreateFailed,
				"status": 400,
			})
			return
		}

		err := s.CreateVehicleItem(c, req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   consts.VehicleItemCreateSuccess,
			"status": 201,
		})
	}
}

func GetItemById(s item.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")

		item, err := s.GetItemById(c, id)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   item,
			"status": 200,
		})
	}
}

func GetVehicleItemById(s item.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")

		item, err := s.GetVehicleItemById(c, id)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   item,
			"status": 200,
		})
	}
}

func UpdateItem(s item.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		var req dtos.UpdateItem
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Update Item Failed",
				Message: "Update Item Failed, bind json err:" + err.Error(),
				Entity:  "item",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  consts.ItemUpdateFailed,
				"status": 400,
			})
			return
		}

		err := s.UpdateItem(c, id, req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   consts.ItemUpdateSuccess,
			"status": 200,
		})
	}
}

func UpdateVehicleItem(s item.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		var req dtos.UpdateVehicleItem
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Update Vehicle Item Failed",
				Message: "Update Vehicle Item Failed, bind json err:" + err.Error(),
				Entity:  "vehicle_item",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  consts.VehicleItemUpdateFailed,
				"status": 400,
			})
			return
		}

		err := s.UpdateVehicleItem(c, id, req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   consts.VehicleItemUpdateSuccess,
			"status": 200,
		})
	}
}

func GetVehicleItemByVehicleId(s item.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		vehicleId := c.Param("vehicle_id")

		item, err := s.GetVehicleItemByVehicleId(c, vehicleId)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   item,
			"status": 200,
		})
	}
}

func DeleteItem(s item.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")

		err := s.DeleteItem(c, id)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   consts.ItemDeleteSuccess,
			"status": 200,
		})
	}
}

func DeleteVehicleItem(s item.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")

		err := s.DeleteVehicleItem(c, id)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   consts.VehicleItemDeleteSuccess,
			"status": 200,
		})
	}
}
