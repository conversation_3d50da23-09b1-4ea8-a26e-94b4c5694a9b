package routes

import (
	"strconv"

	"github.com/SametAvcii/kalkan-auto/pkg/consts"
	"github.com/SametAvcii/kalkan-auto/pkg/domains/payment"
	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/entities"
	"github.com/SametAvcii/kalkan-auto/pkg/log"
	"github.com/SametAvcii/kalkan-auto/pkg/middleware"
	"github.com/gin-gonic/gin"
)

func PaymentRoutes(r *gin.RouterGroup, s payment.Service) {
	r.GET("/payment", middleware.Authorized(), GetPayments(s))
	r.POST("/payment", middleware.Authorized(), CreatePayment(s))
	r.GET("/payment/:id", middleware.Authorized(), GetPaymentById(s))
	r.PUT("/payment/:id", middleware.Authorized(), UpdatePayment(s))
	r.GET("/payment/vehicle/:vehicle_id", middleware.Authorized(), GetPaymentsByVehicleId(s))
	r.DELETE("/payment/:id", middleware.Authorized(), DeletePayment(s))
}

// TODO: add time filters
func GetPayments(s payment.Service) func(c *gin.Context) {
	return func(c *gin.Context) {

		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}

		item, err := s.GetPayments(c, page, perPage)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   item,
			"status": 200,
		})
	}
}

func CreatePayment(s payment.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CreatePayment
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Create Payment Failed",
				Message: "Create Payment Failed, bind json err:" + err.Error(),
				Entity:  "payment",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  consts.PaymentCreateFailed,
				"status": 400,
			})
			return
		}

		err := s.CreatePayment(c, req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   consts.PaymentCreateSuccess,
			"status": 201,
		})
	}
}

func GetPaymentById(s payment.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		item, err := s.GetPaymentById(c, id)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   item,
			"status": 200,
		})
	}
}

func UpdatePayment(s payment.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		var req dtos.UpdatePayment
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Update Payment Failed",
				Message: "Update Payment Failed, bind json err:" + err.Error(),
				Entity:  "payment",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  consts.PaymentUpdateFailed,
				"status": 400,
			})
			return
		}

		err := s.UpdatePayment(c, id, req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   consts.PaymentUpdateSuccess,
			"status": 200,
		})
	}
}

func GetPaymentsByVehicleId(s payment.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		vehicleId := c.Param("vehicle_id")

		item, err := s.GetPaymentsByVehicleId(c, vehicleId)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   item,
			"status": 200,
		})
	}
}

func DeletePayment(s payment.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")

		err := s.DeletePayment(c, id)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   consts.PaymentDeleteSuccess,
			"status": 200,
		})
	}
}
