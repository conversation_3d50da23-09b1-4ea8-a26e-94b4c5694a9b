package entities

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Base struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key" json:"id"`
	CreatedAt   time.Time
	UpdatedAt   time.Time
	DeletedAt   gorm.DeletedAt `gorm:"index"`
	UpdatedBy   uuid.UUID      `json:"updated_by" gorm:"default:null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	UpdatedDate *time.Time     `json:"updated_date" example:"2021-01-01T00:00:00Z"`
	DeletedBy   uuid.UUID      `json:"deleted_by" gorm:"default:null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	DeletedDate *time.Time     `json:"deleted_date" example:"2021-01-01T00:00:00Z"`
	MakedBy     uuid.UUID      `json:"maked_by" gorm:"default:null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

func (g *Base) BeforeCreate(tx *gorm.DB) (err error) {
	g.ID = uuid.New()
	return nil
}

type TotalAmount struct {
	TotalAmount float64 `json:"total_amount"`
}
