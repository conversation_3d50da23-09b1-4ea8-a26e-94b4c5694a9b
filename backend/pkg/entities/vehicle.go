package entities

import (
	"context"

	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/state"
	"github.com/google/uuid"
)

type Vehicle struct {
	Base
	PersonName  string    `json:"person_name"`
	Phone       string    `json:"phone"`
	PlateNumber string    `json:"plate_number"`
	StoreId     uuid.UUID `json:"store_id"`
}

func (v *Vehicle) FromDto(ctx context.Context, req dtos.CreateVehicle) {
	storeId, _ := uuid.Parse(req.StoreId)
	v.PersonName = req.PersonName
	v.Phone = req.Phone
	v.PlateNumber = req.PlateNumber
	v.StoreId = storeId
	v.MakedBy = state.GetCurrentUserID(ctx)
}

func (v *Vehicle) FromUpdateDto(ctx context.Context, req dtos.UpdateVehicle) {
	v.UpdatedBy = state.GetCurrentUserID(ctx)
	v.PersonName = req.PersonName
	v.Phone = req.Phone
	v.PlateNumber = req.PlateNumber
}

func (v *Vehicle) FromListDto(ctx context.Context, vehicle Vehicle) dtos.ListVehicle {
	return dtos.ListVehicle{
		ID:          vehicle.ID,
		Name:        vehicle.PersonName,
		PlateNumber: vehicle.PlateNumber,
		Phone:       vehicle.Phone,
		CreatedAt:   vehicle.CreatedAt.Format("2006-01-02 15:04:05"),
		MakedBy:     vehicle.MakedBy,
	}
}

func (v *Vehicle) ToGetDto(ctx context.Context) dtos.GetVehicle {
	return dtos.GetVehicle{
		ID:          v.ID,
		PersonName:  v.PersonName,
		PlateNumber: v.PlateNumber,
		Phone:       v.Phone,
		StoreId:     v.StoreId,
		CreatedAt:   v.CreatedAt,
		MakedBy:     v.MakedBy,
		UpdatedAt:   v.UpdatedAt,
	}
}
