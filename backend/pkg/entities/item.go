package entities

import (
	"context"

	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/state"
	"github.com/shopspring/decimal"
)

type Item struct {
	Base
	Name     string          `json:"name"`
	Price    decimal.Decimal `json:"price"`
	Quantity int             `json:"quantity"`
	Note     string          `json:"note"`
}

func (i *Item) FromDto(ctx context.Context, req dtos.CreateItem) {
	price, err := decimal.NewFromString(req.Price)
	if err != nil {
		price = decimal.NewFromFloat(0)
	}
	i.UpdatedBy = state.GetCurrentUserID(ctx)
	i.Name = req.Name
	i.Price = price
	i.Quantity = req.Quantity
	i.Note = req.Note
}

func (i *Item) FromUpdateDto(ctx context.Context, req dtos.UpdateItem) {
	price, err := decimal.NewFromString(req.Price)
	if err != nil {
		price = decimal.NewFromFloat(0)
	}

	i.UpdatedBy = state.GetCurrentUserID(ctx)
	i.Name = req.Name
	i.Price = price
	i.Quantity = req.Quantity
	i.Note = req.Note

}
