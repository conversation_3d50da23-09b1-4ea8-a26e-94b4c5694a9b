package entities

import (
	"context"

	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/state"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

type VehicleItem struct {
	Base
	Name      string          `json:"name"`
	VehicleId uuid.UUID       `json:"vehicle_id"`
	ItemId    uuid.UUID       `json:"item_id"`
	Quantity  int             `json:"quantity"`
	Price     decimal.Decimal `json:"price"`
	Note      string          `json:"note"`
	StoreId   uuid.UUID       `json:"store_id"`
	IsPaid    int             `json:"is_paid"` //1: paid, 2: not paid
}

func (vi *VehicleItem) FromDto(ctx context.Context, req dtos.CreateVehicleItem) {

	vehicleId, _ := uuid.Parse(req.VehicleId)
	itemId, _ := uuid.Parse(req.ItemId)
	price, err := decimal.NewFromString(req.Price)
	if err != nil {
		price = decimal.NewFromFloat(0)
	}

	vi.VehicleId = vehicleId
	vi.ItemId = itemId
	vi.Quantity = req.Quantity
	vi.Price = price
	vi.Note = req.Note
	vi.IsPaid = req.IsPaid
	vi.Name = req.Name
	vi.MakedBy = state.GetCurrentUserID(ctx)

}

func (vi *VehicleItem) FromUpdateDto(ctx context.Context, req dtos.UpdateVehicleItem) {
	price, err := decimal.NewFromString(req.Price)
	if err != nil {
		price = decimal.NewFromFloat(0)
	}

	vi.Quantity = req.Quantity
	vi.Price = price
	vi.Note = req.Note
	vi.IsPaid = req.IsPaid
	vi.Name = req.Name
	vi.UpdatedBy = state.GetCurrentUserID(ctx)
}
