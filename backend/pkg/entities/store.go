package entities

import (
	"context"

	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/state"
	"github.com/SametAvcii/kalkan-auto/pkg/utils"
)

type Store struct {
	Base
	Name        string `json:"name"`
	PhoneNumber string `json:"phone_number"`
	PersonName  string `json:"person_name"`
	Note        string `json:"note"`
	SlugName    string `json:"slug_name"`
}

func (s *Store) FromDto(ctx context.Context, req dtos.CreateStore) {

	s.MakedBy = state.GetCurrentUserID(ctx)
	s.Name = req.Name
	s.PhoneNumber = req.PhoneNumber
	s.PersonName = req.PersonName
	s.Note = req.Note
	s.SlugName = utils.Slugify(req.Name)
}

func (s *Store) FromUpdateDto(ctx context.Context, req dtos.UpdateStore) {
	s.UpdatedBy = state.GetCurrentUserID(ctx)
	s.Name = req.Name
	s.PhoneNumber = req.PhoneNumber
	s.PersonName = req.PersonName
	s.Note = req.Note
	s.SlugName = utils.Slugify(req.Name)
}

func (s *Store) SetSlugName() string {
	return utils.Slugify(s.Name)
}

func (s *Store) ToGetDto(ctx context.Context) dtos.GetStore {
	var dto dtos.GetStore
	dto.ID = s.ID
	dto.CreatedAt = s.CreatedAt
	dto.UpdatedAt = s.UpdatedAt
	dto.DeletedAt = s.DeletedAt
	dto.UpdatedBy = s.UpdatedBy
	dto.UpdatedDate = s.UpdatedDate
	dto.DeletedBy = s.DeletedBy
	dto.DeletedDate = s.DeletedDate
	dto.MakedBy = s.MakedBy
	dto.Note = s.Note
	dto.Name = s.Name
	dto.PhoneNumber = s.PhoneNumber
	dto.PersonName = s.PersonName
	dto.SlugName = s.SlugName
	return dto
}
