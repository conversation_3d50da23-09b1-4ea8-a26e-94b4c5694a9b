package entities

import (
	"context"
	"errors"
	"time"

	"github.com/SametAvcii/kalkan-auto/pkg/consts"
	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/state"
	"github.com/SametAvcii/kalkan-auto/pkg/utils"
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

const (
	NotPaid = 2
	Paid    = 1
)

type Payment struct {
	Base
	Amount      decimal.Decimal `json:"amount"`
	Note        string          `json:"note"`
	DueDate     time.Time       `json:"due_date"`
	VehicleId   uuid.UUID       `json:"vehicle_id"`
	PaymentDate time.Time       `json:"payment_date"`
	StoreId     uuid.UUID       `json:"store_id"`
	IsPaid      int             `json:"is_paid"` //1: paid, 2: not paid
}

func (p *Payment) FromDto(ctx context.Context, req dtos.CreatePayment) error {
	dueDate, _ := utils.StringToTime(req.DueDate)
	amount, err := decimal.NewFromString(req.Amount)
	if err != nil {
		return errors.New(consts.InvalidAmount)
	}
	vehicleId, _ := uuid.Parse(req.VehicleId)

	p.MakedBy = state.GetCurrentUserID(ctx)
	p.Amount = amount
	p.Note = req.Note
	p.DueDate = dueDate
	p.VehicleId = vehicleId
	p.IsPaid = req.IsPaid

	return nil
}

func (p *Payment) FromUpdateDto(ctx context.Context, req dtos.UpdatePayment) error {
	if p.IsPaid != Paid && req.IsPaid == Paid {
		p.PaymentDate = time.Now()
	}

	dueDate, _ := utils.StringToTime(req.DueDate)
	amount, err := decimal.NewFromString(req.Amount)
	if err != nil {
		return errors.New(consts.InvalidAmount)
	}

	p.UpdatedBy = state.GetCurrentUserID(ctx)
	p.Amount = amount
	p.Note = req.Note
	p.DueDate = dueDate
	p.IsPaid = req.IsPaid

	return nil
}

func (p *Payment) ToGetDto(ctx context.Context) dtos.GetPayments {
	var dto dtos.GetPayments

	dto.ID = p.ID
	dto.CreatedAt = p.CreatedAt
	dto.UpdatedAt = p.UpdatedAt
	dto.DeletedAt = p.DeletedAt
	dto.UpdatedBy = p.UpdatedBy
	dto.UpdatedDate = p.UpdatedDate
	dto.DeletedBy = p.DeletedBy
	dto.DeletedDate = p.DeletedDate
	dto.MakedBy = p.MakedBy
	dto.Amount = p.Amount
	dto.Note = p.Note
	dto.VehicleId = p.VehicleId
	dto.StoreId = p.StoreId
	dto.IsPaid = p.IsPaid

	if p.DueDate.IsZero() {
		dto.DueDate = ""
	} else {
		dto.DueDate = p.DueDate.String()
	}

	if p.PaymentDate.IsZero() {
		dto.PaymentDate = ""
	} else {
		dto.PaymentDate = p.PaymentDate.String()
	}
	return dto
}
