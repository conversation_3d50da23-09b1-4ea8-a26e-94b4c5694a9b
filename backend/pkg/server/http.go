package server

import (
	"fmt"
	"log"
	"net"
	"os"
	"path"
	"time"

	"github.com/Depado/ginprom"
	"github.com/SametAvcii/kalkan-auto/app/api/routes"
	"github.com/SametAvcii/kalkan-auto/pkg/config"
	"github.com/SametAvcii/kalkan-auto/pkg/database"
	"github.com/SametAvcii/kalkan-auto/pkg/domains/auth"
	"github.com/SametAvcii/kalkan-auto/pkg/domains/item"
	"github.com/SametAvcii/kalkan-auto/pkg/domains/payment"
	"github.com/SametAvcii/kalkan-auto/pkg/domains/store"
	"github.com/SametAvcii/kalkan-auto/pkg/domains/vehicle"
	"github.com/SametAvcii/kalkan-auto/pkg/middleware"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

func LaunchHttpServer(appc config.App, allows config.Allows) {
	log.Println("Starting HTTP Server...")
	gin.SetMode(gin.ReleaseMode)

	app := gin.New()
	app.Use(gin.LoggerWithFormatter(func(log gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] - %s \"%s %s %s %d %s\"\n",
			log.TimeStamp.Format("2006-01-02 15:04:05"),
			log.ClientIP,
			log.Method,
			log.Path,
			log.Request.Proto,
			log.StatusCode,
			log.Latency,
		)
	}))
	app.Use(gin.Recovery())
	app.Use(otelgin.Middleware(appc.Name))
	app.Use(middleware.ClaimIp())
	app.Use(cors.New(cors.Config{
		AllowMethods:     allows.Methods,
		AllowHeaders:     allows.Headers,
		AllowOrigins:     allows.Origins,
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	p := ginprom.New(
		ginprom.Engine(app),
		ginprom.Subsystem("gin"),
		ginprom.Path("/metrics"),
		ginprom.Ignore("/swagger/*any"),
	)
	app.Use(p.Instrument())

	db := database.DBClient()
	api := app.Group("/api/v1")

	authRepo := auth.NewRepo(db)
	authService := auth.NewService(authRepo)
	routes.AuthRoutes(api, authService)

	storeRepo := store.NewRepo(db)
	storeService := store.NewService(storeRepo)
	routes.StoreRoutes(api, storeService)

	vehicleRepo := vehicle.NewRepo(db)
	vehicleService := vehicle.NewService(vehicleRepo)
	routes.VehicleRoutes(api, vehicleService)

	itemRepo := item.NewRepo(db)
	itemService := item.NewService(itemRepo)
	routes.ItemRoutes(api, itemService)

	paymentRepo := payment.NewRepo(db)
	paymentService := payment.NewService(paymentRepo)
	routes.PaymentRoutes(api, paymentService)

	routes.DashboardRoutes(api, paymentService, itemService)

	routes.OutsideRoutes(api, vehicleService, paymentService, itemService)

	if _, err := os.Stat("./dist/index.html"); err == nil {
		// Serve static assets (JS, CSS, images, etc.)
		app.Static("/assets", "./dist/assets")

		// Serve other static files
		app.StaticFile("/vite.svg", "./dist/vite.svg")
		app.StaticFile("/favicon.ico", "./dist/favicon.ico")
		app.StaticFile("/robots.txt", "./dist/robots.txt")
		app.StaticFile("/manifest.json", "./dist/manifest.json")
		app.StaticFile("/logo192.png", "./dist/logo192.png")
		app.StaticFile("/logo512.png", "./dist/logo512.png")

		// Serve the main index.html for root and all SPA routes
		app.GET("/", func(c *gin.Context) {
			c.Header("Content-Type", "text/html; charset=utf-8")
			c.File("./dist/index.html")
		})

		// Fallback for unknown routes (SPA routing)
		app.NoRoute(func(c *gin.Context) {
			// Don't serve .txt files or API routes
			if path.Ext(c.Request.URL.Path) == ".txt" {
				c.Status(404)
				return
			}
			// Don't serve index.html for API routes
			if len(c.Request.URL.Path) > 4 && c.Request.URL.Path[:5] == "/api/" {
				c.Status(404)
				return
			}
			// Don't serve index.html for assets
			if len(c.Request.URL.Path) > 7 && c.Request.URL.Path[:8] == "/assets/" {
				c.Status(404)
				return
			}
			// Serve index.html for all other routes (SPA routing)
			c.Header("Content-Type", "text/html; charset=utf-8")
			c.File("./dist/index.html")
		})
	}

	fmt.Println("Server is running on port " + appc.Port)
	app.Run(net.JoinHostPort(appc.Host, appc.Port))
}
