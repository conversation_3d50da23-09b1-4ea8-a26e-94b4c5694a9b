package consts

const ( // User
	UserName = "username"
)

const ( //Error
	LoginFailed       = "Giriş Başarısız, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	CreateUserFailed  = "Kullanıcı Oluşturulamadı, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	CreateUserSuccess = "Kullanıcı Oluşturuldu"
	UserNotFound      = "Kullanıcı Bulunamadı"
	UpdateUserFailed  = "Kullanıcı Güncellenemedi, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
)

const ( // Store
	StoreCreateFailed  = "Müşteri Oluşturulamadı, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	StoreCreateSuccess = "Müşteri Oluşturuldu"
	StoreGetFailed     = "Müşteriler Getirilemedi, Lütfen Daha Sonra Tekrar Deneyin"
	StoreGetSuccess    = "Müşteriler Getirildi"
	StoreNotFound      = "Müşteri Bulunamadı"
	StoreUpdateFailed  = "Müşteri Güncellenemedi, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	StoreUpdateSuccess = "Müşteri Başarıyla Güncellendi"
	StoreDeleteFailed  = "Müşteri Silinemedi, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	StoreDeleteSuccess = "Müşteri Başarıyla Silindi"
	StoreAlreadyExist  = "Müşteri Daha Önce Eklenmiş, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	StoreSlugNameSetSuccess   = "Müşteri Slug Name Oluşturuldu"
)

const ( // Vehicle
	VehicleCreateFailed  = "Araç Oluşturulamadı, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	VehicleCreateSuccess = "Araç Oluşturuldu"
	VehicleGetFailed     = "Araçlar Getirilemedi, Lütfen Daha Sonra Tekrar Deneyin"
	VehicleGetSuccess    = "Araçlar Getirildi"
	VehicleAlreadyExist  = "Araç Daha Önce Eklenmiş"
	VehicleNotFound      = "Araç Bulunamadı"
	VehicleUpdateFailed  = "Araç Güncellenemedi, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	VehicleUpdateSuccess = "Araç Başarıyla Güncellendi"
	VehicleDeleteFailed  = "Araç Silinemedi, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	VehicleDeleteSuccess = "Araç Başarıyla Silindi"
)

const ( // Item
	ItemCreateFailed  = "Parça Oluşturulamadı, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	ItemCreateSuccess = "Parça Oluşturuldu"
	ItemGetFailed     = "Parçalar Getirilemedi, Lütfen Daha Sonra Tekrar Deneyin"
	ItemGetSuccess    = "Parçalar Getirildi"
	ItemNotFound      = "Parça Bulunamadı"
	ItemUpdateFailed  = "Parça Güncellenemedi, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	ItemUpdateSuccess = "Parça Başarıyla Güncellendi"
	ItemDeleteFailed  = "Parça Silinemedi, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	ItemDeleteSuccess = "Parça Başarıyla Silindi"
)

const ( // VehicleItem
	VehicleItemCreateFailed  = "Parça Satışı Oluşturulamadı, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	VehicleItemCreateSuccess = "Parça Satışı Oluşturuldu"
	VehicleItemGetFailed     = "Parça Satışları Getirilemedi, Lütfen Daha Sonra Tekrar Deneyin"
	VehicleItemGetSuccess    = "Parça Satışları Getirildi"
	VehicleItemUpdateFailed  = "Araç Detayları Güncellenemedi, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	VehicleItemUpdateSuccess = "Araç Detayları Başarıyla Güncellendi"
	VehicleItemNotFound      = "Araç Detayları Bulunamadı"
	VehicleItemDeleteFailed  = "Araç Detayları Silinemedi, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	VehicleItemDeleteSuccess = "Araç Detayları Başarıyla Silindi"
)

const ( // Amount
	PaymentCreateFailed      = "Borç/Ödeme Oluşturulamadı, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	PaymentCreateSuccess     = "Borç/Ödeme Oluşturuldu"
	PaymentGetFailed         = "Borç/Ödeme Getirilemedi, Lütfen Daha Sonra Tekrar Deneyin"
	PaymentGetSuccess        = "Borç/Ödemeler Getirildi"
	InvalidAmount            = "Borç/Ödemeler, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	VehicleOrStoreMustBeFill = "Araç veya Müşteri Seçilmek Zorundadır"
	PaymentUpdateFailed      = "Borç/Ödeme Güncellenemedi, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	PaymentUpdateSuccess     = "Borç/Ödeme Başarıyla Güncellendi"
	PaymentDeleteFailed      = "Borç/Ödeme Silinemedi, Lütfen Bilgileri Kontrol Edip Tekrar Deneyin"
	PaymentDeleteSuccess     = "Borç/Ödeme Başarıyla Silindi"
)

const (
	TimeLayout = "2006-01-02 15:04:05"
)
