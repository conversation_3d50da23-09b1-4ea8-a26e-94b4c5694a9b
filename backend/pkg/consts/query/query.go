package query

var Query = map[string]string{
	"id":                               "id = ?",
	"user_id":                          "user_id = ?",
	"name":                             "name = ?",
	"email":                            "email = ?",
	"entity_id":                        "entity_id = ?",
	"entity_id_in":                     "entity_id IN (?)",
	"entity":                           "entity = ?",
	"entity_type":                      "entity_type = ?",
	"created_at_between":               "created_at BETWEEN ? AND ?",
	"vehicle_id":                       "vehicle_id = ?",
	"store_id":                         "store_id = ?",
	"plate_number":                     "plate_number = ?",
	"created_at_gte":                   "created_at >= ?",
	"payment_date_gte":                 "payment_date >= ?",
	"is_paid":                          "is_paid = ?",
	"deleted_at":                       "deleted_at IS NULL",
	"select_total_amount":              "COALESCE(SUM(amount::numeric), 0) AS total_amount",
	"select_total_price":               "COALESCE(SUM(price::numeric), 0) AS total_amount",
	"select_total_item_price":          "COALESCE(SUM(price::numeric * quantity), 0) AS total_amount",
	"name_ilike":                       "name ILIKE ?",
	"name_or_phone_ilike":              "(name ILIKE ? OR phone_number ILIKE ?)",
	"person_name_or_phone_plate_ilike": "(person_name ILIKE ? OR phone ILIKE ? OR plate_number ILIKE ?)",
	"slug_name":                        "slug_name = ?",
}

const (
	WhereID               string = "id"
	WhereCreatedAtBetween string = "created_at_between"
	WhereCreatedAtGTE     string = "created_at_gte"
	WherePaymentDateGTE   string = "payment_date_gte"

	WhereUserID                      string = "user_id"
	WhereEntityID                    string = "entity_id"
	WhereEntityIDIN                  string = "entity_id_in"
	WhereEntity                      string = "entity"
	WhereEntityType                  string = "entity_type"
	WhereEmail                       string = "email"
	WhereName                        string = "name"
	WhereVehicleID                   string = "vehicle_id"
	WhereStoreID                     string = "store_id"
	WherePlateNumber                 string = "plate_number"
	WhereIsPaid                      string = "is_paid"
	WhereDeletedAt                   string = "deleted_at"
	WhereNameILike                   string = "name_ilike"
	WhereNameOrPhoneILike            string = "name_or_phone_ilike"
	WherePersonNameOrPhonePlateILike string = "person_name_or_phone_plate_ilike"

	SelectTotalAmount    string = "select_total_amount"
	SelectTotalPrice     string = "select_total_price"
	SelectTotalItemPrice string = "select_total_item_price"
	OrderByCreatedAtDesc string = "created_at desc"

	WhereSlugName string = "slug_name"
)

func BuildQuery(keys ...string) string {
	if len(keys) == 0 {
		return ""
	}
	if len(keys) == 1 {
		return Query[keys[0]]
	}

	var query string
	for _, key := range keys {
		query += Query[key] + " AND "
	}

	return query[:len(query)-5] // remove last " AND "
}

type QueryBuilder struct {
	Keys []QueryKey `json:"keys"`
}

type QueryKey struct {
	Key    string
	Values []interface{}
	Skip   bool
}
