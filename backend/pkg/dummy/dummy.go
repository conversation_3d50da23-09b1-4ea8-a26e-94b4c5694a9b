package dummy

import (
	"github.com/SametAvcii/kalkan-auto/pkg/consts"
	"github.com/SametAvcii/kalkan-auto/pkg/database"
	"github.com/SametAvcii/kalkan-auto/pkg/entities"
	"github.com/SametAvcii/kalkan-auto/pkg/utils"
)

func CreateDummy() {
	CreateDummyUser()
}

func CreateDummyUser() {
	db := database.DBClient()

	var user entities.User
	db.Where(consts.UserName+" = ?", "samet").First(&user)
	if user.Username != "" {
		return
	}

	pass := utils.Bcrypt("SametAvci05")
	db.Create(&entities.User{
		Username: "samet",
		Password: pass,
		Name:     "Samet Avci",
	})
}
