package middleware

import (
	"net/http"
	"strings"

	"github.com/SametAvcii/kalkan-auto/pkg/config"
	"github.com/SametAvcii/kalkan-auto/pkg/consts"
	"github.com/SametAvcii/kalkan-auto/pkg/entities"
	"github.com/SametAvcii/kalkan-auto/pkg/log"
	"github.com/SametAvcii/kalkan-auto/pkg/state"
	"github.com/SametAvcii/kalkan-auto/pkg/utils"
	"github.com/gin-gonic/gin"
)

func ClaimIp() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("CurrentIP", c.ClientIP())
		c.Set(state.CurrentUserIP, c.ClientIP())
		c.Next()
	}
}

func Authorized() gin.HandlerFunc {
	return func(c *gin.Context) {
		cfg_app := config.ReadValue().App
		jwt := utils.JwtWrapper{
			SecretKey: cfg_app.JwtSecret,
		}
		bearer := c.Request.Header.Get("Authorization")
		if bearer == "" {
			log.CreateLog(&entities.Log{
				Title:   consts.LoginFailed,
				Message: "Authorization header is empty",
				Type:    "error",
				Entity:  "user",
				Ip:      c.ClientIP(),
			},
			)

			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": consts.LoginFailed})
			return
		}
		if !strings.HasPrefix(bearer, "Bearer ") {
			log.CreateLog(&entities.Log{
				Title:   consts.LoginFailed,
				Message: "Authorization header is not Bearer",
				Type:    "error",
				Entity:  "user",
				Ip:      c.ClientIP(),
			},
			)
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": consts.LoginFailed})
			return
		}
	
		token := strings.Split(bearer, "Bearer ")[1]
		if jwt.ValidateToken(token) {
			claims, _ := jwt.ParseToken(token)
			c.Set(state.CurrentUserID, claims.UserId)
			c.Set(state.CurrentUserIP, c.ClientIP())
			c.Set(state.CurrentUserName, claims.UserName)

			c.Next()
		} else {
			log.CreateLog(&entities.Log{
				Title:   consts.LoginFailed,
				Message: "Token is not valid",
				Type:    "error",
				Entity:  "user",
				Ip:      c.ClientIP(),
			},
			)

			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": consts.LoginFailed})
			return
		}
	}
}
