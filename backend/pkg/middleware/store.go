package middleware

import (
	"net/http"
	"strings"

	"github.com/SametAvcii/kalkan-auto/pkg/config"
	"github.com/SametAvcii/kalkan-auto/pkg/consts"
	"github.com/SametAvcii/kalkan-auto/pkg/entities"
	"github.com/SametAvcii/kalkan-auto/pkg/log"
	"github.com/SametAvcii/kalkan-auto/pkg/state"
	"github.com/SametAvcii/kalkan-auto/pkg/utils"
	"github.com/gin-gonic/gin"
)

func StoreAuthorized() gin.HandlerFunc {
	return func(c *gin.Context) {
		cfg_app := config.ReadValue().App
		jwt := utils.JwtWrapper{
			SecretKey: cfg_app.JwtStoreSecret,
		}
		bearer := c.Request.Header.Get("Authorization")
		if bearer == "" {
			log.CreateLog(&entities.Log{
				Title:   consts.LoginFailed,
				Message: "Authorization header is empty",
				Type:    "error",
				Entity:  "user",
				Ip:      c.<PERSON>(),
			},
			)

			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": consts.LoginFailed})
			return
		}
		if !strings.HasPrefix(bearer, "Bearer ") {
			log.CreateLog(&entities.Log{
				Title:   consts.LoginFailed,
				Message: "Authorization header is not Bearer",
				Type:    "error",
				Entity:  "user",
				Ip:      c.ClientIP(),
			},
			)
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": consts.LoginFailed})
			return
		}

		token := strings.Split(bearer, "Bearer ")[1]
		if jwt.ValidateStoreToken(token) {
			claims, _ := jwt.ParseStoreToken(token)
			c.Set(state.CurrentSlugName, claims.SlugName)
			c.Set(state.CurrentStoreID, claims.StoreId)
			c.Next()
		} else {
			log.CreateLog(&entities.Log{
				Title:   consts.LoginFailed,
				Message: "Token is not valid",
				Type:    "error",
				Entity:  "user",
				Ip:      c.ClientIP(),
			},
			)

			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": consts.LoginFailed})
			return
		}
	}
}
