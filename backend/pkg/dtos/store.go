package dtos

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type CreateStore struct {
	Name        string `json:"name"`
	PhoneNumber string `json:"phone_number"`
	PersonName  string `json:"person_name"`
	Note        string `json:"note"`
}

type UpdateStore struct {
	Name        string `json:"name"`
	PhoneNumber string `json:"phone_number"`
	PersonName  string `json:"person_name"`
	Note        string `json:"note"`
}

type GetStore struct {
	ID                   uuid.UUID `gorm:"type:uuid;primary_key" json:"id"`
	CreatedAt            time.Time
	UpdatedAt            time.Time
	DeletedAt            gorm.DeletedAt `gorm:"index"`
	UpdatedBy            uuid.UUID      `json:"updated_by" gorm:"default:null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	UpdatedDate          *time.Time     `json:"updated_date" example:"2021-01-01T00:00:00Z"`
	DeletedBy            uuid.UUID      `json:"deleted_by" gorm:"default:null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	DeletedDate          *time.Time     `json:"deleted_date" example:"2021-01-01T00:00:00Z"`
	MakedBy              uuid.UUID      `json:"maked_by" gorm:"default:null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Name                 string         `json:"name"`
	PhoneNumber          string         `json:"phone_number"`
	PersonName           string         `json:"person_name"`
	Note                 string         `json:"note"`
	TotalAmount          float64        `json:"total_amount"`
	RemainingAmount      float64        `json:"remaining_amount"`
	PaidAmount           float64        `json:"paid_amount"`
	SlugName             string         `json:"slug_name"`
	DeletedVehiclesCount int64          `json:"deleted_vehicles_count,omitempty"`
}
