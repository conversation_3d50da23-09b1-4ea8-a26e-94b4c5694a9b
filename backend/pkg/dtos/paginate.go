package dtos

type PaginatedData struct {
	Page       int64       `json:"page,omitempty" example:"1"`
	PerPage    int64       `json:"per_page,omitempty" example:"10"`
	Total      int64       `json:"total,omitempty" example:"100"`
	TotalPages int         `json:"total_pages,omitempty" example:"10"`
	Rows       interface{} `json:"rows,omitempty" swaggertype:"array,object"`
}

type PaginatedDataWithAmount struct {
	Page            int64       `json:"page,omitempty" example:"1"`
	PerPage         int64       `json:"per_page,omitempty" example:"10"`
	Total           int64       `json:"total,omitempty" example:"100"`
	TotalPages      int         `json:"total_pages,omitempty" example:"10"`
	Rows            interface{} `json:"rows,omitempty" swaggertype:"array,object"`
	TotalAmount     float64     `json:"total_amount"`
	RemainingAmount float64     `json:"remaining_amount"`
	PaidAmount      float64     `json:"paid_amount"`
}
