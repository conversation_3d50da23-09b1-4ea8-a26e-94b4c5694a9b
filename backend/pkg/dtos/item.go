package dtos

type CreateItem struct {
	Name     string `json:"name"`
	Price    string `json:"price"`
	Quantity int    `json:"quantity"`
	Note     string `json:"note"`
}

type CreateVehicleItem struct {
	VehicleId string `json:"vehicle_id"`
	ItemId    string `json:"item_id"`
	Quantity  int    `json:"quantity"`
	Price     string `json:"price"`
	Note      string `json:"note"`
	IsPaid    int    `json:"is_paid"` //1: paid, 2: not paid
	Name      string `json:"name"`
}

type UpdateVehicleItem struct {
	Quantity int    `json:"quantity"`
	Price    string `json:"price"`
	Note     string `json:"note"`
	IsPaid   int    `json:"is_paid"` //1: paid, 2: not paid
	Name     string `json:"name"`
}

type UpdateItem struct {
	Name     string `json:"name"`
	Price    string `json:"price"`
	Quantity int    `json:"quantity"`
	Note     string `json:"note"`
}
