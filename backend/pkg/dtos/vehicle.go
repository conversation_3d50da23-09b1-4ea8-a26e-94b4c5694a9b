package dtos

import (
	"time"

	"github.com/google/uuid"
)

type CreateVehicle struct {
	PlateNumber string `json:"plate_number"`
	StoreId     string `json:"store_id"`
	Phone       string `json:"phone"`
	PersonName  string `json:"person_name"`
}

type UpdateVehicle struct {
	PlateNumber string `json:"plate_number"`
	Phone       string `json:"phone"`
	PersonName  string `json:"person_name"`
}

type ListVehicle struct {
	ID             uuid.UUID `gorm:"type:uuid;primary_key" json:"id"`
	Name           string    `json:"person_name"`
	PlateNumber    string    `json:"plate_number"`
	Phone          string    `json:"phone"`
	CreatedAt      string    `json:"CreatedAt"`
	Items          []string  `json:"items"`
	TotalPrice     float64   `json:"total_payment"`
	RemainingPrice float64   `json:"remaining"`
	MakedBy        uuid.UUID `json:"maked_by"`
}

type GetVehicle struct {
	ID              uuid.UUID `gorm:"type:uuid;primary_key" json:"id"`
	CreatedAt       time.Time
	UpdatedAt       time.Time
	MakedBy         uuid.UUID `json:"maked_by" gorm:"default:null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	PersonName      string    `json:"person_name"`
	Phone           string    `json:"phone"`
	PlateNumber     string    `json:"plate_number"`
	StoreId         uuid.UUID `json:"store_id"`
	TotalAmount     float64   `json:"total_amount"`
	RemainingAmount float64   `json:"remaining_amount"`
	PaidAmount      float64   `json:"paid_amount"`
}
