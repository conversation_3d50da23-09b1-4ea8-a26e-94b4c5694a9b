package dtos

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type CreatePayment struct {
	Amount    string `json:"amount"`
	Note      string `json:"note"`
	DueDate   string `json:"due_date"`
	VehicleId string `json:"vehicle_id"`
	StoreId   string `json:"store_id"`
	IsPaid    int    `json:"is_paid"` //1: paid, 2: not paid
}

type UpdatePayment struct {
	Amount  string `json:"amount"`
	Note    string `json:"note"`
	DueDate string `json:"due_date"`
	IsPaid  int    `json:"is_paid"` //1: paid, 2: not paid
}

type ChangePaymentStatus struct {
	IsPaid int `json:"is_paid"` //1: paid, 2: not paid
}

type DashboardPayment struct {
	Daily   ListPayment `json:"daily_total"`
	Weekly  ListPayment `json:"weekly_total"`
	Monthly ListPayment `json:"monthly_total"`
}

type ListPayment struct {
	Total  float64             `json:"total"`
	Stores []ListStorePayments `json:"stores"`
}

type ListStorePayments struct {
	ID      string  `json:"id"`
	Name    string  `json:"name"`
	Payment float64 `json:"payment"`
}

type GetPayments struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key" json:"id"`
	CreatedAt   time.Time
	UpdatedAt   time.Time
	DeletedAt   gorm.DeletedAt  `gorm:"index"`
	UpdatedBy   uuid.UUID       `json:"updated_by" gorm:"default:null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	UpdatedDate *time.Time      `json:"updated_date" example:"2021-01-01T00:00:00Z"`
	DeletedBy   uuid.UUID       `json:"deleted_by" gorm:"default:null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	DeletedDate *time.Time      `json:"deleted_date" example:"2021-01-01T00:00:00Z"`
	MakedBy     uuid.UUID       `json:"maked_by" gorm:"default:null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Amount      decimal.Decimal `json:"amount"`
	Note        string          `json:"note"`
	DueDate     string          `json:"due_date" gorm:"default:null"`
	VehicleId   uuid.UUID       `json:"vehicle_id"`
	PaymentDate string          `json:"payment_date"`
	StoreId     uuid.UUID       `json:"store_id"`
	IsPaid      int             `json:"is_paid"` //1: paid, 2: not paid
}
