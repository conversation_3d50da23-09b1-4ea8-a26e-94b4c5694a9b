package config

import (
	"log"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
	"gopkg.in/yaml.v3"
)

type Config struct {
	App      App      `yaml:"app"`
	Database Database `yaml:"database"`
	Allows   Allows   `yaml:"allows"`
	Whatsapp Whatsapp `yaml:"whatsapp"`
}

type App struct {
	Name            string `yaml:"name"`
	Port            string `yaml:"port"`
	Host            string `yaml:"host"`
	JwtIssuer       string `yaml:"jwt_issuer"`
	JwtExpire       int    `yaml:"jwt_expire"`
	JwtSecret       string `yaml:"jwt_secret"`
	JwtStoreSecret  string `yaml:"jwt_store_secret"`
	ClientID        string `yaml:"client_id"`
	OneSignalAPIKey string `yaml:"onesignal_api_key"`
	OneSignalAPPID  string `yaml:"onesignal_app_id"`
}

type Database struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
	User string `yaml:"user"`
	Pass string `yaml:"pass"`
	Name string `yaml:"name"`
}

type Allows struct {
	Methods []string `yaml:"methods"`
	Origins []string `yaml:"origins"`
	Headers []string `yaml:"headers"`
}

type Whatsapp struct {
	ApiKey string `yaml:"api_key"`
}

func InitConfig() *Config {
	var configs Config
	file_name, _ := filepath.Abs("./config.yaml")
	yaml_file, _ := os.ReadFile(file_name)
	yaml.Unmarshal(yaml_file, &configs)
	return &configs
}

var configs *Config

func LoadEnv() {
	err := godotenv.Load()
	if err != nil {
		log.Println("No .env file found, skipping...")
	}
}

// -------------------- Read Config --------------------

func ReadValue() *Config {
	if configs != nil {
		return configs
	}

	LoadEnv()

	// Fallback için yaml oku
	var yamlConfig Config
	filename, err := filepath.Abs("./config.yaml")
	if err == nil {
		yamlFile, err := os.ReadFile(filename)
		if err == nil {
			yaml.Unmarshal(yamlFile, &yamlConfig)
		}
	}

	// Env öncelikli config
	configs = &Config{
		App: App{
			Name:            getEnv("APP_NAME", yamlConfig.App.Name),
			Port:            getEnv("APP_PORT", yamlConfig.App.Port),
			Host:            getEnv("APP_HOST", yamlConfig.App.Host),
			JwtIssuer:       getEnv("APP_JWT_ISSUER", yamlConfig.App.JwtIssuer),
			JwtSecret:       getEnv("APP_JWT_SECRET", yamlConfig.App.JwtSecret),
			JwtExpire:       getEnvInt("APP_JWT_EXPIRE", yamlConfig.App.JwtExpire),
			ClientID:        getEnv("APP_CLIENT_ID", yamlConfig.App.ClientID),
			OneSignalAPIKey: getEnv("APP_ONESIGNAL_API_KEY", yamlConfig.App.OneSignalAPIKey),
			OneSignalAPPID:  getEnv("APP_ONESIGNAL_APP_ID", yamlConfig.App.OneSignalAPPID),
		},
		Database: Database{
			Host: getEnv("DB_HOST", yamlConfig.Database.Host),
			Port: getEnv("DB_PORT", yamlConfig.Database.Port),
			User: getEnv("DB_USER", yamlConfig.Database.User),
			Pass: getEnv("DB_PASS", yamlConfig.Database.Pass),
			Name: getEnv("DB_NAME", yamlConfig.Database.Name),
		},

		Allows: Allows{
			Methods: getEnvSlice("ALLOWS_METHODS", yamlConfig.Allows.Methods),
			Origins: getEnvSlice("ALLOWS_ORIGINS", yamlConfig.Allows.Origins),
			Headers: getEnvSlice("ALLOWS_HEADERS", yamlConfig.Allows.Headers),
		},
		Whatsapp: Whatsapp{
			ApiKey: getEnv("WHATSAPP_API_KEY", yamlConfig.Whatsapp.ApiKey),
		},
	}

	return configs
}

// -------------------- Helper Functions --------------------

func getEnv(key string, fallback string) string {
	if val := os.Getenv(key); val != "" {
		return val
	}
	return fallback
}

func getEnvInt(key string, fallback int) int {
	if val := os.Getenv(key); val != "" {
		if i, err := strconv.Atoi(val); err == nil {
			return i
		}
	}
	return fallback
}

func getEnvSlice(key string, fallback []string) []string {
	if val := os.Getenv(key); val != "" {
		return strings.Split(val, ",")
	}
	return fallback
}
