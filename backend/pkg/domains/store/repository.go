package store

import (
	"context"
	"errors"
	"math"
	"sort"

	"github.com/SametAvcii/kalkan-auto/pkg/consts"
	"github.com/SametAvcii/kalkan-auto/pkg/consts/query"
	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/entities"
	"github.com/SametAvcii/kalkan-auto/pkg/log"
	"github.com/SametAvcii/kalkan-auto/pkg/state"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	CreateStore(ctx context.Context, store *entities.Store) error
	GetStores(ctx context.Context, page, perpage int, key string) (*dtos.PaginatedData, error)
	GetStoreById(ctx context.Context, id string) (*entities.Store, error)
	UpdateStore(ctx context.Context, id string, req dtos.UpdateStore) error
	DeleteStore(ctx context.Context, id string) error
	SetSlugName(ctx context.Context)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) CreateStore(ctx context.Context, store *entities.Store) error {

	var isExist entities.Store
	r.db.Where(query.BuildQuery(query.WhereSlugName), store.SlugName).First(&isExist)

	if isExist.ID != uuid.Nil {
		return errors.New(consts.StoreAlreadyExist)
	}

	err := r.db.WithContext(ctx).Create(store).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Create Store Error",
			Message: "Create Store Error: " + err.Error(),
			Entity:  "store",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.StoreCreateFailed)
	}
	return nil

}

func (r *repository) GetStores(ctx context.Context, page, perpage int, key string) (*dtos.PaginatedData, error) {
	var stores []entities.Store

	var count int64

	storeQuery := r.db.WithContext(ctx).Model(&entities.Store{}).Order(query.OrderByCreatedAtDesc).Where(query.BuildQuery(query.WhereNameILike), "%"+key+"%")
	storeQuery.Count(&count)
	if err := storeQuery.Limit(perpage).Offset((page - 1) * perpage).Find(&stores).Error; err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Get Stores Error",
			Message: "Get Stores Error: " + err.Error(),
			Entity:  "store",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return nil, errors.New(consts.StoreGetFailed)
	}

	var dtoItems []dtos.GetStore
	for _, store := range stores {
		dto := store.ToGetDto(ctx)

		var totalPayment entities.TotalAmount

		_ = r.db.WithContext(ctx).Model(&entities.Payment{}).Select(query.BuildQuery(query.SelectTotalAmount)).
			Where(query.BuildQuery(query.WhereStoreID, query.WhereIsPaid), store.ID, entities.Paid).Scan(&totalPayment).Error

		var totalAmount entities.TotalAmount
		_ = r.db.WithContext(ctx).Model(&entities.VehicleItem{}).Select(query.BuildQuery(query.SelectTotalPrice)).
			Where(query.BuildQuery(query.WhereStoreID), store.ID).Scan(&totalAmount).Error

		dto.TotalAmount = totalAmount.TotalAmount
		dto.PaidAmount = totalPayment.TotalAmount
		dto.RemainingAmount = dto.TotalAmount - dto.PaidAmount

		dtoItems = append(dtoItems, dto)
	}

	dtoItems = SortStoresByRemainingAmount(dtoItems)
	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(perpage),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(perpage))),
		Rows:       dtoItems,
	}, nil

}

func (r *repository) GetStoreById(ctx context.Context, id string) (*entities.Store, error) {
	var store entities.Store
	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), id).First(&store).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Get Store By Id Error",
			Message: "Get Store By Id Error: " + err.Error(),
			Entity:  "store",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return nil, errors.New(consts.StoreGetFailed)
	}
	return &store, nil
}

func (r *repository) UpdateStore(ctx context.Context, id string, req dtos.UpdateStore) error {
	var store entities.Store
	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), id).First(&store).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Update Store Error",
			Message: "Update Store Error: " + err.Error(),
			Entity:  "store",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.StoreUpdateFailed)
	}
	store.FromUpdateDto(ctx, req)
	err = r.db.WithContext(ctx).Model(&entities.Store{}).Where(query.BuildQuery(query.WhereID), id).Updates(store).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Update Store Error",
			Message: "Update Store Error: " + err.Error(),
			Entity:  "store",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.StoreUpdateFailed)
	}
	return nil
}

func (r *repository) DeleteStore(ctx context.Context, id string) error {
	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), id).Delete(&entities.Store{}).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Delete Store Error",
			Message: "Delete Store Error: " + err.Error(),
			Entity:  "store",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.StoreDeleteFailed)
	}
	return nil
}

func (r *repository) SetSlugName(ctx context.Context) {
	var stores []entities.Store
	r.db.WithContext(ctx).Find(&stores)
	for _, store := range stores {
		store.SlugName = store.SetSlugName()
		r.db.WithContext(ctx).Model(&store).Updates(store)
	}

}
func SortStoresByRemainingAmount(dtoItems []dtos.GetStore) []dtos.GetStore {
	sort.SliceStable(dtoItems, func(i, j int) bool {
		if dtoItems[i].RemainingAmount == 0 && dtoItems[j].RemainingAmount > 0 {
			return false
		}
		if dtoItems[i].RemainingAmount > 0 && dtoItems[j].RemainingAmount == 0 {
			return true
		}
		return dtoItems[i].RemainingAmount > dtoItems[j].RemainingAmount
	})
	return dtoItems
}
