package store

import (
	"context"

	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/entities"
)

type Service interface {
	CreateStore(ctx context.Context, req dtos.CreateStore) error
	GetStores(ctx context.Context, page, parpage int, key string) (*dtos.PaginatedData, error)
	GetStoreById(ctx context.Context, id string) (*entities.Store, error)
	UpdateStore(ctx context.Context, id string, req dtos.UpdateStore) error
	DeleteStore(ctx context.Context, id string) error
	SetSlugName(ctx context.Context)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) GetStores(ctx context.Context, page, perpage int, key string) (*dtos.PaginatedData, error) {
	return s.repository.GetStores(ctx, page, perpage, key)
}

func (s *service) CreateStore(ctx context.Context, req dtos.CreateStore) error {
	var store entities.Store
	store.FromDto(ctx, req)
	return s.repository.CreateStore(ctx, &store)

}

func (s *service) GetStoreById(ctx context.Context, id string) (*entities.Store, error) {
	return s.repository.GetStoreById(ctx, id)
}

func (s *service) UpdateStore(ctx context.Context, id string, req dtos.UpdateStore) error {
	return s.repository.UpdateStore(ctx, id, req)
}

func (s *service) DeleteStore(ctx context.Context, id string) error {
	return s.repository.DeleteStore(ctx, id)
}

func (s *service) SetSlugName(ctx context.Context) {
	s.repository.SetSlugName(ctx)
}
