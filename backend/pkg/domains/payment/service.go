package payment

import (
	"context"

	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/entities"
	"github.com/SametAvcii/kalkan-auto/pkg/log"
)

type Service interface {
	GetPayments(ctx context.Context, page, perpage int) (*dtos.PaginatedData, error)
	CreatePayment(ctx context.Context, req dtos.CreatePayment) error
	GetPaymentById(ctx context.Context, id string) (dtos.GetPayments, error)
	UpdatePayment(ctx context.Context, id string, req dtos.UpdatePayment) error
	GetPaymentsByVehicleId(ctx context.Context, vehicleId string) ([]dtos.GetPayments, error)
	DeletePayment(ctx context.Context, id string) error
	GetPaymentsDashboard(ctx context.Context, page, perpage int) (dtos.DashboardPayment, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) GetPayments(ctx context.Context, page, perpage int) (*dtos.PaginatedData, error) {
	return s.repository.GetPayments(ctx, page, perpage)
}

func (s *service) CreatePayment(ctx context.Context, req dtos.CreatePayment) error {
	var payment entities.Payment
	err := payment.FromDto(ctx, req)
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Create Payment Error",
			Message: "Create Payment Error: " + err.Error(),
			Entity:  "payment",
			Type:    "error",
		})
		return err
	}
	return s.repository.CreatePayment(ctx, &payment)
}

func (s *service) GetPaymentById(ctx context.Context, id string) (dtos.GetPayments, error) {
	return s.repository.GetPaymentById(ctx, id)
}

func (s *service) UpdatePayment(ctx context.Context, id string, req dtos.UpdatePayment) error {

	return s.repository.UpdatePayment(ctx, id, req)
}

func (s *service) GetPaymentsByVehicleId(ctx context.Context, vehicleId string) ([]dtos.GetPayments, error) {
	return s.repository.GetPaymentsByVehicleId(ctx, vehicleId)
}

func (s *service) DeletePayment(ctx context.Context, id string) error {
	return s.repository.DeletePayment(ctx, id)
}

func (s *service) GetPaymentsDashboard(ctx context.Context, page, perpage int) (dtos.DashboardPayment, error) {
	return s.repository.GetPaymentsDashboard(ctx, page, perpage)
}
