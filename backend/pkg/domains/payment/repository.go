package payment

import (
	"context"
	"errors"
	"math"
	"time"

	"github.com/SametAvcii/kalkan-auto/pkg/consts"
	"github.com/SametAvcii/kalkan-auto/pkg/consts/query"
	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/entities"
	"github.com/SametAvcii/kalkan-auto/pkg/log"
	"github.com/SametAvcii/kalkan-auto/pkg/state"
	"gorm.io/gorm"
)

type Repository interface {
	CreatePayment(ctx context.Context, payment *entities.Payment) error
	GetPayments(ctx context.Context, page, perpage int) (*dtos.PaginatedData, error)
	UpdatePayment(ctx context.Context, id string, req dtos.UpdatePayment) error
	GetPaymentById(ctx context.Context, id string) (dtos.GetPayments, error)
	GetPaymentsByVehicleId(ctx context.Context, vehicleId string) ([]dtos.GetPayments, error)
	DeletePayment(ctx context.Context, id string) error
	GetPaymentsDashboard(ctx context.Context, page, perpage int) (dtos.DashboardPayment, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) CreatePayment(ctx context.Context, payment *entities.Payment) error {
	var vehicle entities.Vehicle
	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), payment.VehicleId).First(&vehicle).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Vehicle or Store Not Found create payment",
			Message: "Vehicle or Store not found: ",
			Entity:  "payment",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.VehicleOrStoreMustBeFill)
	}
	if payment.IsPaid == entities.Paid {
		payment.PaymentDate = time.Now()
	}
	payment.StoreId = vehicle.StoreId

	err = r.db.WithContext(ctx).Create(&payment).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Create Payment Error",
			Message: "Create Payment Error: " + err.Error(),
			Entity:  "payment",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.PaymentCreateFailed)
	}
	return nil
}

func (r *repository) GetPayments(ctx context.Context, page, perpage int) (*dtos.PaginatedData, error) {
	var payments []entities.Payment
	var count int64

	pageQuery := r.db.Model(&entities.Payment{}).Order(query.OrderByCreatedAtDesc)
	pageQuery.Count(&count)
	if err := pageQuery.WithContext(ctx).Limit(perpage).Offset((page - 1) * perpage).Find(&payments).Error; err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Get Payments Error",
			Message: "Get Payments Error: " + err.Error(),
			Entity:  "item",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return nil, errors.New(consts.PaymentGetFailed)
	}

	var dtoItems []dtos.GetPayments
	for _, payment := range payments {
		dtoItems = append(dtoItems, payment.ToGetDto(ctx))
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(perpage),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(perpage))),
		Rows:       dtoItems,
	}, nil
}

func (r *repository) GetPaymentById(ctx context.Context, id string) (dtos.GetPayments, error) {
	var payment entities.Payment
	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), id).First(&payment).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Get Payment By Id Error",
			Message: "Get Payment By Id Error: " + err.Error(),
			Entity:  "payment",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return dtos.GetPayments{}, errors.New(consts.PaymentGetFailed)
	}
	dtoItem := payment.ToGetDto(ctx)
	return dtoItem, nil
}

func (r *repository) UpdatePayment(ctx context.Context, id string, req dtos.UpdatePayment) error {
	var payment entities.Payment
	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), id).First(&payment).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Update Payment Error",
			Message: "Update Payment Error: " + err.Error(),
			Entity:  "payment",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.PaymentUpdateFailed)
	}

	err = payment.FromUpdateDto(ctx, req)
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Update Payment Error",
			Message: "Update Payment Error: " + err.Error(),
			Entity:  "payment",
			Type:    "error",
		})
		return err
	}

	err = r.db.WithContext(ctx).Model(&entities.Payment{}).Where(query.BuildQuery(query.WhereID), id).Updates(payment).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Update Payment Error",
			Message: "Update Payment Error: " + err.Error(),
			Entity:  "payment",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.PaymentUpdateFailed)
	}
	return nil
}

func (r *repository) DeletePayment(ctx context.Context, id string) error {
	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), id).Delete(&entities.Payment{}).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Delete Payment Error",
			Message: "Delete Payment Error: " + err.Error(),
			Entity:  "payment",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.PaymentDeleteFailed)
	}
	return nil
}

func (r *repository) GetPaymentsByVehicleId(ctx context.Context, vehicleId string) ([]dtos.GetPayments, error) {
	var dtoItems []dtos.GetPayments
	var payments []entities.Payment
	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereVehicleID), vehicleId).Find(&payments).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return dtoItems, nil
	}

	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Get Payments By Vehicle Id Error",
			Message: "Get Payments By Vehicle Id Error: " + err.Error(),
			Entity:  "payment",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return nil, errors.New(consts.PaymentGetFailed)
	}

	for _, payment := range payments {
		dtoItems = append(dtoItems, payment.ToGetDto(ctx))
	}

	return dtoItems, nil
}

func (r *repository) GetPaymentsDashboard(ctx context.Context, page, perpage int) (dtos.DashboardPayment, error) {
	var payments []entities.Payment

	// Fetch all payments
	if err := r.db.WithContext(ctx).Model(&entities.Payment{}).
		Where(query.BuildQuery(query.WherePaymentDateGTE), time.Now().AddDate(0, -1, 0)).Find(&payments).Error; err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Get Payments Dashboard Error",
			Message: "Get Payments Dashboard Error: " + err.Error(),
			Entity:  "payment",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return dtos.DashboardPayment{}, errors.New(consts.PaymentGetFailed)
	}

	var dashboard dtos.DashboardPayment

	dailyStorePayments := make(map[string]dtos.ListStorePayments)
	weeklyStorePayments := make(map[string]dtos.ListStorePayments)
	monthlyStorePayments := make(map[string]dtos.ListStorePayments)

	now := time.Now()

	for _, payment := range payments {
		if payment.IsPaid != entities.Paid {
			continue
		}

		paymentDate := payment.CreatedAt
		duration := now.Sub(paymentDate)

		if duration.Hours() <= 24 {
			dashboard.Daily.Total += payment.Amount.InexactFloat64()

			_, exists := dailyStorePayments[payment.StoreId.String()]
			if !exists && len(dashboard.Daily.Stores) < 10 {
				var store entities.Store
				r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), payment.StoreId).First(&store)
				storeDto := dtos.ListStorePayments{
					ID:      store.ID.String(),
					Name:    store.Name,
					Payment: 0,
				}
				dailyStorePayments[payment.StoreId.String()] = storeDto
			}

			addToStorePayments(dailyStorePayments, payment.StoreId.String(), payment.Amount.InexactFloat64())

		}

		if duration.Hours() <= 24*7 {
			dashboard.Weekly.Total += payment.Amount.InexactFloat64()
			_, exists := weeklyStorePayments[payment.StoreId.String()]
			if !exists && len(dashboard.Weekly.Stores) < 10 {
				var store entities.Store
				r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), payment.StoreId).First(&store)
				storeDto := dtos.ListStorePayments{
					ID:      store.ID.String(),
					Name:    store.Name,
					Payment: 0,
				}
				weeklyStorePayments[payment.StoreId.String()] = storeDto
			}

			addToStorePayments(weeklyStorePayments, payment.StoreId.String(), payment.Amount.InexactFloat64())

		}

		if duration.Hours() <= 24*30 {
			dashboard.Monthly.Total += payment.Amount.InexactFloat64()
			_, exists := monthlyStorePayments[payment.StoreId.String()]
			if !exists && len(dashboard.Monthly.Stores) < 10 {
				var store entities.Store
				r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), payment.StoreId).First(&store)
				storeDto := dtos.ListStorePayments{
					ID:      store.ID.String(),
					Name:    store.Name,
					Payment: 0,
				}
				monthlyStorePayments[payment.StoreId.String()] = storeDto
			}
			addToStorePayments(monthlyStorePayments, payment.StoreId.String(), payment.Amount.InexactFloat64())
		}

	}

	dashboard.Daily.Stores = mapToListStorePayments(dailyStorePayments)
	dashboard.Weekly.Stores = mapToListStorePayments(weeklyStorePayments)
	dashboard.Monthly.Stores = mapToListStorePayments(monthlyStorePayments)

	return dashboard, nil
}

func addToStorePayments(storeMap map[string]dtos.ListStorePayments, storeID string, amount float64) {
	store := storeMap[storeID]
	store.Payment += amount
	storeMap[storeID] = store
}

func mapToListStorePayments(storeMap map[string]dtos.ListStorePayments) []dtos.ListStorePayments {
	var stores []dtos.ListStorePayments
	for _, store := range storeMap {
		stores = append(stores, store)
	}
	return stores
}
