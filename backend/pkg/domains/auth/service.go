package auth

import (
	"errors"
	"time"

	"github.com/SametAvcii/kalkan-auto/pkg/config"
	"github.com/SametAvcii/kalkan-auto/pkg/consts"
	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/entities"
	"github.com/SametAvcii/kalkan-auto/pkg/log"
	"github.com/SametAvcii/kalkan-auto/pkg/utils"
)

type Service interface {
	Login(payload *dtos.AuthenticationRequest) (dtos.AuthenticationResponse, error)
	CreateUser(payload dtos.CreateUserReqDto) error
	StoreLogin(payload *dtos.AuthenticationRequest) (dtos.AuthenticationResponse, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) CreateUser(payload dtos.CreateUserReqDto) error {
	return s.repository.CreateUser(payload)
}

func (s *service) Login(payload *dtos.AuthenticationRequest) (dtos.AuthenticationResponse, error) {
	var resp dtos.AuthenticationResponse
	user, err := s.repository.GetUserByUserName(payload.Username)

	if err != nil {
		log.CreateLog(&entities.Log{
			Title:    consts.LoginFailed,
			Message:  err.Error(),
			Type:     "error",
			Entity:   "user",
			EntityID: user.ID,
			UserID:   user.ID,
		})
		return resp, errors.New(consts.LoginFailed)
	}
	if !utils.Compare(user.Password, payload.Password) {
		log.CreateLog(&entities.Log{
			Title:    consts.LoginFailed,
			Message:  consts.LoginFailed,
			Type:     "error",
			Entity:   "user",
			EntityID: user.ID,
			UserID:   user.ID,
		})
		return resp, errors.New(consts.LoginFailed)
	}

	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().App.JwtSecret,
		Expire:    int(config.ReadValue().App.JwtExpire),
	}

	token, err := jwt.GenerateJWT(user.Username, user.ID.String())
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:    consts.LoginFailed,
			Message:  err.Error(),
			Type:     "error",
			Entity:   "user",
			EntityID: user.ID,
			UserID:   user.ID,
		})

		return resp, errors.New(consts.LoginFailed)
	}

	resp.Token = token
	resp.Expires = time.Now().Add(time.Duration(config.ReadValue().App.JwtExpire) * time.Hour)
	resp.IsSucceeded = true

	return resp, nil
}

func (s *service) StoreLogin(payload *dtos.AuthenticationRequest) (dtos.AuthenticationResponse, error) {
	var resp dtos.AuthenticationResponse
	store, err := s.repository.GetStoreBySlugName(payload.Username)

	if err != nil {
		log.CreateLog(&entities.Log{
			Title:    consts.LoginFailed,
			Message:  err.Error(),
			Type:     "error",
			Entity:   "store",
			EntityID: store.ID,
			UserID:   store.ID,
		})
		return resp, errors.New(consts.LoginFailed)
	}
	if payload.Password != "kalkan" {
		log.CreateLog(&entities.Log{
			Title:    consts.LoginFailed,
			Message:  consts.LoginFailed,
			Type:     "error",
			Entity:   "store",
			EntityID: store.ID,
			UserID:   store.ID,
		})
		return resp, errors.New(consts.LoginFailed)
	}

	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().App.JwtStoreSecret,
		Expire:    int(config.ReadValue().App.JwtExpire),
	}

	token, err := jwt.GenerateStoreJWT(store.SlugName, store.ID.String())
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:    consts.LoginFailed,
			Message:  err.Error(),
			Type:     "error",
			Entity:   "user",
			EntityID: store.ID,
			UserID:   store.ID,
		})

		return resp, errors.New(consts.LoginFailed)
	}

	resp.Token = token
	resp.Expires = time.Now().Add(time.Duration(config.ReadValue().App.JwtExpire) * time.Hour)
	resp.IsSucceeded = true
	return resp, nil
}
