package item

import (
	"context"

	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/entities"
)

type Service interface {
	GetItems(ctx context.Context, key string, page, perpage int) (*dtos.PaginatedDataWithAmount, error)
	CreateItem(ctx context.Context, req dtos.CreateItem) error
	GetVehicleItems(ctx context.Context, page, perpage int, vehicleId, key string) (*dtos.PaginatedDataWithAmount, error)
	CreateVehicleItem(ctx context.Context, req dtos.CreateVehicleItem) error
	GetItemById(ctx context.Context, id string) (*entities.Item, error)
	UpdateVehicleItem(ctx context.Context, id string, req dtos.UpdateVehicleItem) error
	UpdateItem(ctx context.Context, id string, req dtos.UpdateItem) error
	GetVehicleItemById(ctx context.Context, id string) (*entities.VehicleItem, error)
	GetVehicleItemByVehicleId(ctx context.Context, vehicleId string) (*entities.VehicleItem, error)
	DeleteVehicleItem(ctx context.Context, id string) error
	DeleteItem(ctx context.Context, id string) error
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) GetItems(ctx context.Context, key string, page, perpage int) (*dtos.PaginatedDataWithAmount, error) {
	return s.repository.GetItems(ctx, key, page, perpage)
}

func (s *service) CreateItem(ctx context.Context, req dtos.CreateItem) error {
	var item entities.Item
	item.FromDto(ctx, req)
	return s.repository.CreateItem(ctx, &item)
}

func (s *service) CreateVehicleItem(ctx context.Context, req dtos.CreateVehicleItem) error {
	var vehicleItem entities.VehicleItem
	vehicleItem.FromDto(ctx, req)
	return s.repository.CreateVehicleItem(ctx, &vehicleItem)
}

func (s *service) GetVehicleItems(ctx context.Context, page, perpage int, vehicleId, key string) (*dtos.PaginatedDataWithAmount, error) {
	return s.repository.GetVehicleItems(ctx, page, perpage, vehicleId, key)
}

func (s *service) GetItemById(ctx context.Context, id string) (*entities.Item, error) {
	return s.repository.GetItemById(ctx, id)
}

func (s *service) GetVehicleItemById(ctx context.Context, id string) (*entities.VehicleItem, error) {
	return s.repository.GetVehicleItemById(ctx, id)
}

func (s *service) UpdateItem(ctx context.Context, id string, req dtos.UpdateItem) error {

	return s.repository.UpdateItem(ctx, id, req)
}

func (s *service) UpdateVehicleItem(ctx context.Context, id string, req dtos.UpdateVehicleItem) error {
	return s.repository.UpdateVehicleItem(ctx, id, req)
}

func (s *service) GetVehicleItemByVehicleId(ctx context.Context, vehicleId string) (*entities.VehicleItem, error) {
	return s.repository.GetVehicleItemByVehicleId(ctx, vehicleId)
}

func (s *service) DeleteVehicleItem(ctx context.Context, id string) error {
	return s.repository.DeleteVehicleItem(ctx, id)
}

func (s *service) DeleteItem(ctx context.Context, id string) error {
	return s.repository.DeleteItem(ctx, id)
}
