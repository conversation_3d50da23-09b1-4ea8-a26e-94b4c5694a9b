package item

import (
	"context"
	"errors"
	"math"

	"github.com/SametAvcii/kalkan-auto/pkg/consts"
	"github.com/SametAvcii/kalkan-auto/pkg/consts/query"
	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/entities"
	"github.com/SametAvcii/kalkan-auto/pkg/log"
	"github.com/SametAvcii/kalkan-auto/pkg/state"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type Repository interface {
	CreateItem(ctx context.Context, item *entities.Item) error
	GetItems(ctx context.Context, key string, page, perpage int) (*dtos.PaginatedDataWithAmount, error)
	CreateVehicleItem(ctx context.Context, vehicleItem *entities.VehicleItem) error
	GetVehicleItems(ctx context.Context, page, perpage int, vehicleId, key string) (*dtos.PaginatedDataWithAmount, error)
	GetItemById(ctx context.Context, id string) (*entities.Item, error)
	UpdateVehicleItem(ctx context.Context, id string, req dtos.UpdateVehicleItem) error
	UpdateItem(ctx context.Context, id string, req dtos.UpdateItem) error
	GetVehicleItemById(ctx context.Context, id string) (*entities.VehicleItem, error)
	GetVehicleItemByVehicleId(ctx context.Context, vehicleId string) (*entities.VehicleItem, error)
	DeleteVehicleItem(ctx context.Context, id string) error
	DeleteItem(ctx context.Context, id string) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) CreateItem(ctx context.Context, item *entities.Item) error {
	err := r.db.WithContext(ctx).Create(&item).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Create Item Error",
			Message: "Create Item Error: " + err.Error(),
			Entity:  "item",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.ItemCreateFailed)
	}
	return nil
}

func (r *repository) GetItems(ctx context.Context, key string, page, perpage int) (*dtos.PaginatedDataWithAmount, error) {
	var items []entities.Item
	var count int64

	itemQuery := r.db.Model(&entities.Item{}).Order(query.OrderByCreatedAtDesc).Where(query.BuildQuery(query.WhereNameILike), "%"+key+"%")
	itemQuery.Count(&count)
	if err := itemQuery.WithContext(ctx).Limit(perpage).Offset((page - 1) * perpage).
		Find(&items).Error; err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Get Items Error",
			Message: "Get Items Error: " + err.Error(),
			Entity:  "item",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return nil, errors.New(consts.ItemGetFailed)
	}

	var totalAmount entities.TotalAmount
	_ = r.db.WithContext(ctx).Model(&entities.Item{}).Select(query.BuildQuery(query.SelectTotalItemPrice)).Scan(&totalAmount).Error

	return &dtos.PaginatedDataWithAmount{
		Page:        int64(page),
		PerPage:     int64(perpage),
		Total:       count,
		TotalPages:  int(math.Ceil(float64(count) / float64(perpage))),
		Rows:        items,
		TotalAmount: totalAmount.TotalAmount,
	}, nil
}

func (r *repository) CreateVehicleItem(ctx context.Context, vehicleItem *entities.VehicleItem) error {
	var vehicle entities.Vehicle
	var item entities.Item

	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), vehicleItem.VehicleId).First(&vehicle).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Vehicle Not Found create vehicle item",
			Message: "Vehicle not found: " + err.Error(),
			Entity:  "vehicle_item",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.VehicleNotFound)
	}

	r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), vehicleItem.ItemId).First(&item)
	/*if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Item Not Found create vehicle item",
			Message: "Item not found: " + err.Error(),
			Entity:  "vehicle_item",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.ItemNotFound)
	}*/

	if item.Name == "" {
		var item entities.Item
		item.Name = vehicleItem.Name
		item.Price = vehicleItem.Price
		item.Quantity = 10
		err = r.db.WithContext(ctx).Create(&item).Error
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Item Not Created, create vehicle item",
				Message: "Item not created: " + err.Error(),
				Entity:  "item",
				Type:    "error",
				Ip:      state.GetCurrentUserIP(ctx),
				UserID:  state.GetCurrentUserID(ctx),
			})

		}
		vehicleItem.ItemId = item.ID
	}

	if item.Price == decimal.Zero {
		vehicleItem.Price = item.Price
	}

	vehicleItem.StoreId = vehicle.StoreId
	tx := r.db.WithContext(ctx).Begin()
	err = tx.WithContext(ctx).Create(&vehicleItem).Error
	if err != nil {
		tx.Rollback()
		log.CreateLog(&entities.Log{
			Title:   "Vehicle Item Not Created",
			Message: "Vehicle Item not created: " + err.Error(),
			Entity:  "vehicle_item",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.VehicleItemCreateFailed)
	}

	item.Quantity = item.Quantity - vehicleItem.Quantity
	err = tx.WithContext(ctx).Model(&entities.Item{}).Where(query.BuildQuery(query.WhereID), item.ID).Updates(&item).Error
	if err != nil {
		tx.Rollback()
		log.CreateLog(&entities.Log{
			Title:   "Item Not Updated",
			Message: "Item not updated: " + err.Error(),
			Entity:  "item",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.VehicleItemUpdateFailed)
	}

	tx.Commit()
	return nil
}

func (r *repository) GetVehicleItems(ctx context.Context, page, perpage int, vehicleId, key string) (*dtos.PaginatedDataWithAmount, error) {
	var items []entities.VehicleItem
	var count int64

	itemsQuery := r.db.WithContext(ctx).Model(&entities.VehicleItem{}).Where(query.BuildQuery(query.WhereVehicleID), vehicleId).
		Order(query.OrderByCreatedAtDesc).Where(query.BuildQuery(query.WhereNameILike), "%"+key+"%")
	itemsQuery.Count(&count)
	if err := itemsQuery.Limit(perpage).Offset((page - 1) * perpage).Find(&items).Error; err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Get Vehicle Items Error",
			Message: "Get Vehicle Items Error: " + err.Error(),
			Entity:  "vehicle_item",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})

		return nil, errors.New(consts.VehicleItemGetFailed)
	}

	var totalPayment entities.TotalAmount
	_ = r.db.WithContext(ctx).Model(&entities.Payment{}).Select(query.BuildQuery(query.SelectTotalAmount)).
		Where(query.BuildQuery(query.WhereVehicleID, query.WhereIsPaid), vehicleId, entities.Paid).Scan(&totalPayment).Error

	var totalAmount entities.TotalAmount
	_ = r.db.WithContext(ctx).Model(&entities.VehicleItem{}).Select(query.BuildQuery(query.SelectTotalPrice)).
		Where(query.BuildQuery(query.WhereVehicleID), vehicleId).Scan(&totalAmount).Error

	return &dtos.PaginatedDataWithAmount{
		Page:            int64(page),
		PerPage:         int64(perpage),
		Total:           count,
		TotalPages:      int(math.Ceil(float64(count) / float64(perpage))),
		Rows:            items,
		TotalAmount:     totalAmount.TotalAmount,
		PaidAmount:      totalPayment.TotalAmount,
		RemainingAmount: totalAmount.TotalAmount - totalPayment.TotalAmount,
	}, nil
}

func (r *repository) GetItemById(ctx context.Context, id string) (*entities.Item, error) {
	var item entities.Item
	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), id).First(&item).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Item Not Found",
			Message: "Item not found: " + err.Error(),
			Entity:  "item",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return nil, errors.New(consts.ItemNotFound)
	}
	return &item, nil
}

func (r *repository) GetVehicleItemById(ctx context.Context, id string) (*entities.VehicleItem, error) {
	var vehicleItem entities.VehicleItem
	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), id).First(&vehicleItem).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Vehicle Item Not Found",
			Message: "Vehicle Item not found: " + err.Error(),
			Entity:  "vehicle_item",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return nil, errors.New(consts.VehicleItemNotFound)
	}
	return &vehicleItem, nil
}

func (r *repository) UpdateItem(ctx context.Context, id string, req dtos.UpdateItem) error {
	var item entities.Item
	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), id).First(&item).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Item Not Found",
			Message: "Item not found: " + err.Error(),
			Entity:  "item",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.ItemNotFound)
	}
	item.FromUpdateDto(ctx, req)

	err = r.db.WithContext(ctx).Model(&entities.Item{}).Where(query.BuildQuery(query.WhereID), id).Updates(&item).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Item Not Updated",
			Message: "Item not updated: " + err.Error(),
			Entity:  "item",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.ItemUpdateFailed)
	}
	return nil
}

func (r *repository) UpdateVehicleItem(ctx context.Context, id string, req dtos.UpdateVehicleItem) error {

	var vehicleItem entities.VehicleItem
	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), id).First(&vehicleItem).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Vehicle Item Not Found",
			Message: "Vehicle Item not found: " + err.Error(),
			Entity:  "vehicle_item",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.VehicleItemNotFound)
	}
	vehicleItem.FromUpdateDto(ctx, req)
	err = r.db.WithContext(ctx).Model(&entities.VehicleItem{}).Where(query.BuildQuery(query.WhereID), id).Updates(&vehicleItem).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Vehicle Item Not Updated",
			Message: "Vehicle Item not updated: " + err.Error(),
			Entity:  "vehicle_item",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.VehicleItemUpdateFailed)
	}
	return nil
}

func (r *repository) GetVehicleItemByVehicleId(ctx context.Context, vehicleId string) (*entities.VehicleItem, error) {
	var vehicleItem entities.VehicleItem
	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereVehicleID), vehicleId).First(&vehicleItem).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return &vehicleItem, nil
	}

	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Vehicle Item Not Found",
			Message: "Vehicle Item not found: " + err.Error(),
			Entity:  "vehicle_item",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return nil, errors.New(consts.VehicleItemNotFound)
	}
	return &vehicleItem, nil
}

func (r *repository) DeleteVehicleItem(ctx context.Context, id string) error {
	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), id).Delete(&entities.VehicleItem{}).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Delete Vehicle Item Error",
			Message: "Delete Vehicle Item Error: " + err.Error(),
			Entity:  "vehicle_item",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.VehicleItemDeleteFailed)
	}
	return nil
}

func (r *repository) DeleteItem(ctx context.Context, id string) error {
	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), id).Delete(&entities.Item{}).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Delete Item Error",
			Message: "Delete Item Error: " + err.Error(),
			Entity:  "item",
			Type:    "error",
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.ItemDeleteFailed)
	}
	return nil
}
