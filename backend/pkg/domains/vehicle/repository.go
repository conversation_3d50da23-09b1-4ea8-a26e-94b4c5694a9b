package vehicle

import (
	"context"
	"errors"
	"math"
	"sort"
	"time"

	"github.com/SametAvcii/kalkan-auto/pkg/consts"
	"github.com/SametAvcii/kalkan-auto/pkg/consts/query"
	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/entities"
	"github.com/SametAvcii/kalkan-auto/pkg/log"
	"github.com/SametAvcii/kalkan-auto/pkg/state"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type Repository interface {
	CreateVehicle(ctx context.Context, vehicle *entities.Vehicle) error
	GetVehicles(ctx context.Context, page, perpage int) (*dtos.PaginatedData, error)
	GetVehicleById(ctx context.Context, id string) (dtos.GetVehicle, error)
	UpdateVehicle(ctx context.Context, id string, req dtos.UpdateVehicle) error
	DeleteVehicle(ctx context.Context, id string) error
	RestoreVehicle(ctx context.Context, id string) error
	GetVehiclesByStoreId(ctx context.Context, id string, page, perpage int, key string) (*dtos.PaginatedDataWithAmount, error)
	GetVehiclesBySlugName(ctx context.Context, name string, page, perpage int, key string) (*dtos.PaginatedDataWithAmount, error)
	GetDeletedVehiclesByStoreId(ctx context.Context, id string, page, perpage int, key string) (*dtos.PaginatedDataWithAmount, error)
	GetDeletedVehiclesBySlugName(ctx context.Context, name string, page, perpage int, key string) (*dtos.PaginatedDataWithAmount, error)
	GetStoresWithDeletedVehicles(ctx context.Context, key string) ([]dtos.GetStore, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) CreateVehicle(ctx context.Context, vehicle *entities.Vehicle) error {
	var store entities.Store
	//var oldVehicle entities.Vehicle

	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), vehicle.StoreId).First(&store).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Store Not Found create vehicle",
			Message: "Store not found: " + err.Error(),
			Entity:  "vehicle",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.StoreNotFound)

	}

	/*_ = r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereDeletedAt)).Where(query.BuildQuery(query.WhereStoreID), store.ID).
		Where(query.BuildQuery(query.WherePlateNumber), vehicle.PlateNumber).First(&oldVehicle).Error
	if oldVehicle.ID != uuid.Nil {
		log.CreateLog(&entities.Log{
			Title:   "Vehicle already exist",
			Message: "Vehicle already exist: ",
			Entity:  "vehicle",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.VehicleAlreadyExist)
	}*/

	err = r.db.WithContext(ctx).Create(&vehicle).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Vehicle Not Created",
			Message: "Vehicle not created: " + err.Error(),
			Entity:  "vehicle",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.VehicleCreateFailed)
	}
	return nil
}

func (r *repository) GetVehicles(ctx context.Context, page, perpage int) (*dtos.PaginatedData, error) {
	var vehicles []entities.Vehicle
	var count int64

	query := r.db.WithContext(ctx).Model(&entities.Vehicle{}).Order(query.OrderByCreatedAtDesc)
	query.Count(&count)
	if err := query.Limit(perpage).Offset((page - 1) * perpage).
		Find(&vehicles).Error; err != nil {
		return nil, errors.New(consts.VehicleGetFailed)
	}
	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(perpage),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(perpage))),
		Rows:       vehicles,
	}, nil

}

func (r *repository) GetVehicleById(ctx context.Context, id string) (dtos.GetVehicle, error) {
	var vehicle entities.Vehicle
	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), id).First(&vehicle).Error
	if err != nil {
		return dtos.GetVehicle{}, errors.New(consts.VehicleGetFailed)
	}

	//find vehicle items
	var vehicleItems []entities.VehicleItem
	_ = r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereVehicleID), id).Find(&vehicleItems).Error
	_, totalAmount := processVehicleItems(vehicleItems)

	var payments []entities.Payment
	_ = r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereVehicleID), id).Where(query.BuildQuery(query.WhereIsPaid), entities.Paid).Find(&payments).Error

	getDto := vehicle.ToGetDto(ctx)
	getDto.TotalAmount = totalAmount
	getDto.RemainingAmount, getDto.PaidAmount = calculateDebt(decimal.NewFromFloat(totalAmount), payments)

	return getDto, nil
}

func (r *repository) UpdateVehicle(ctx context.Context, id string, req dtos.UpdateVehicle) error {
	var vehicle entities.Vehicle
	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), id).First(&vehicle).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Vehicle Not Found",
			Message: "Vehicle not found: " + err.Error(),
			Entity:  "vehicle",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.VehicleNotFound)
	}
	vehicle.FromUpdateDto(ctx, req)
	err = r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), id).Updates(&vehicle).Error
	if err != nil {
		log.CreateLog(&entities.Log{
			Title:   "Vehicle Not Updated",
			Message: "Vehicle not updated: " + err.Error(),
			Entity:  "vehicle",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.VehicleUpdateFailed)
	}
	return nil
}

func (r *repository) GetVehiclesByStoreId(ctx context.Context, id string, page, perpage int, key string) (*dtos.PaginatedDataWithAmount, error) {
	var vehicles []entities.Vehicle
	var listDtos []dtos.ListVehicle
	var totalAmount, totalPayment entities.TotalAmount

	// Get all vehicles and calculate RemainingPrice
	pageQuery := r.db.WithContext(ctx).Model(&entities.Vehicle{}).Order(query.OrderByCreatedAtDesc).
		Where(query.BuildQuery(query.WhereStoreID), id)

	if key != "" {
		pageQuery = pageQuery.Where(query.BuildQuery(query.WherePersonNameOrPhonePlateILike), "%"+key+"%", "%"+key+"%", "%"+key+"%")
	}

	err := pageQuery.Find(&vehicles).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.New(consts.VehicleNotFound)
	}
	if err != nil {
		return nil, errors.New(consts.VehicleGetFailed)
	}

	// Create DTOs and calculate RemainingAmount
	for _, vehicle := range vehicles {
		var vehicleItems []entities.VehicleItem
		r.db.WithContext(ctx).Model(&entities.VehicleItem{}).Where(query.BuildQuery(query.WhereVehicleID), vehicle.ID).Find(&vehicleItems)

		dtoItem := vehicle.FromListDto(ctx, vehicle)
		dtoItem.Items, dtoItem.TotalPrice = processVehicleItems(vehicleItems)

		// Query payments
		var payments []entities.Payment
		_ = r.db.WithContext(ctx).Model(&entities.Payment{}).Where(query.BuildQuery(query.WhereVehicleID, query.WhereIsPaid), vehicle.ID, entities.Paid).Find(&payments)

		dtoItem.RemainingPrice, _ = calculateDebt(decimal.NewFromFloat(dtoItem.TotalPrice), payments)
		listDtos = append(listDtos, dtoItem)
	}

	// Sort by RemainingAmount
	listDtos = SortVehicleByRemainingAmount(listDtos)

	// Calculate total counts before pagination
	totalCount := int64(len(listDtos))
	totalPages := int(math.Ceil(float64(totalCount) / float64(perpage)))

	// Paginate
	start := (page - 1) * perpage
	end := start + perpage
	if start > len(listDtos) {
		start = len(listDtos)
	}
	if end > len(listDtos) {
		end = len(listDtos)
	}
	paginatedListDtos := listDtos[start:end]

	// Calculate total amounts
	_ = r.db.WithContext(ctx).Model(&entities.Payment{}).Select(query.BuildQuery(query.SelectTotalAmount)).
		Where(query.BuildQuery(query.WhereStoreID, query.WhereIsPaid), id, entities.Paid).Scan(&totalPayment).Error
	_ = r.db.WithContext(ctx).Model(&entities.VehicleItem{}).Select(query.BuildQuery(query.SelectTotalPrice)).
		Where(query.BuildQuery(query.WhereStoreID), id).Scan(&totalAmount).Error

	return &dtos.PaginatedDataWithAmount{
		Page:            int64(page),
		PerPage:         int64(perpage),
		Total:           totalCount,
		TotalPages:      totalPages,
		Rows:            paginatedListDtos,
		TotalAmount:     totalAmount.TotalAmount,
		PaidAmount:      totalPayment.TotalAmount,
		RemainingAmount: totalAmount.TotalAmount - totalPayment.TotalAmount,
	}, nil
}

func (r *repository) DeleteVehicle(ctx context.Context, id string) error {
	tx := r.db.WithContext(ctx).Begin()

	// Update DeletedBy and DeletedDate before deleting
	now := time.Now()
	var vehicle entities.Vehicle
	err := tx.WithContext(ctx).Where(query.BuildQuery(query.WhereID), id).First(&vehicle).Error
	if err != nil {
		tx.Rollback()
		log.CreateLog(&entities.Log{
			Title:   "Vehicle Not Found",
			Message: "Vehicle not found: " + err.Error(),
			Entity:  "vehicle",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.VehicleNotFound)
	}

	// Update the vehicle with deletion info
	vehicle.DeletedBy = state.GetCurrentUserID(ctx)
	vehicle.DeletedDate = &now
	err = tx.WithContext(ctx).Model(&vehicle).Updates(map[string]interface{}{
		"deleted_by":   vehicle.DeletedBy,
		"deleted_date": vehicle.DeletedDate,
	}).Error
	if err != nil {
		tx.Rollback()
		log.CreateLog(&entities.Log{
			Title:   "Vehicle Deletion Info Update Failed",
			Message: "Vehicle deletion info update failed: " + err.Error(),
			Entity:  "vehicle",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.VehicleUpdateFailed)
	}

	// Delete vehicle items
	err = tx.Where(query.BuildQuery(query.WhereVehicleID), id).Delete(&entities.VehicleItem{}).Error
	if err != nil {
		tx.Rollback()
		log.CreateLog(&entities.Log{
			Title:   "Vehicle Item Not Deleted",
			Message: "Vehicle item not deleted: " + err.Error(),
			Entity:  "vehicle",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.VehicleItemDeleteFailed)
	}

	// Delete payments
	err = tx.Where(query.BuildQuery(query.WhereVehicleID), id).Delete(&entities.Payment{}).Error
	if err != nil {
		tx.Rollback()
		log.CreateLog(&entities.Log{
			Title:   "Payment Not Deleted",
			Message: "Payment not deleted: " + err.Error(),
			Entity:  "vehicle",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.PaymentDeleteFailed)
	}

	// Delete the vehicle
	err = tx.WithContext(ctx).Where(query.BuildQuery(query.WhereID), id).Delete(&vehicle).Error
	if err != nil {
		tx.Rollback()
		log.CreateLog(&entities.Log{
			Title:   "Vehicle Not Deleted",
			Message: "Vehicle not deleted: " + err.Error(),
			Entity:  "vehicle",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.VehicleDeleteFailed)
	}

	tx.Commit()
	return nil
}

func (r *repository) GetVehiclesBySlugName(ctx context.Context, name string, page, perpage int, key string) (*dtos.PaginatedDataWithAmount, error) {
	var vehicles []entities.Vehicle
	var listDtos []dtos.ListVehicle
	var totalAmount, totalPayment entities.TotalAmount

	// Find store by slug name
	var store entities.Store
	err := r.db.WithContext(ctx).Debug().Where(query.BuildQuery(query.WhereSlugName), name).First(&store).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.New(consts.StoreNotFound)
	}

	// Get all vehicles for this store (without pagination)
	var vehicleQuery *gorm.DB
	if key != "" {
		vehicleQuery = r.db.WithContext(ctx).Model(&entities.Vehicle{}).Order(query.OrderByCreatedAtDesc).
			Where(query.BuildQuery(query.WhereStoreID, query.WherePersonNameOrPhonePlateILike), store.ID, "%"+key+"%", "%"+key+"%", "%"+key+"%")
	} else {
		vehicleQuery = r.db.WithContext(ctx).Model(&entities.Vehicle{}).Order(query.OrderByCreatedAtDesc).
			Where(query.BuildQuery(query.WhereStoreID), store.ID)
	}

	err = vehicleQuery.Find(&vehicles).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.New(consts.VehicleNotFound)
	}
	if err != nil {
		return nil, errors.New(consts.VehicleGetFailed)
	}

	// Create DTOs and calculate RemainingAmount for all vehicles
	for _, vehicle := range vehicles {
		var vehicleItems []entities.VehicleItem
		r.db.WithContext(ctx).Model(&entities.VehicleItem{}).Where(query.BuildQuery(query.WhereVehicleID), vehicle.ID).Find(&vehicleItems)

		dtoItem := vehicle.FromListDto(ctx, vehicle)
		dtoItem.Items, dtoItem.TotalPrice = processVehicleItems(vehicleItems)

		// Query payments
		var payments []entities.Payment
		_ = r.db.WithContext(ctx).Model(&entities.Payment{}).Where(query.BuildQuery(query.WhereVehicleID, query.WhereIsPaid), vehicle.ID, entities.Paid).Find(&payments)

		dtoItem.RemainingPrice, _ = calculateDebt(decimal.NewFromFloat(dtoItem.TotalPrice), payments)
		listDtos = append(listDtos, dtoItem)
	}

	// Sort all vehicles by RemainingAmount
	listDtos = SortVehicleByRemainingAmount(listDtos)

	// Calculate total counts before pagination
	totalCount := int64(len(listDtos))
	totalPages := int(math.Ceil(float64(totalCount) / float64(perpage)))

	// Apply pagination to the sorted results
	start := (page - 1) * perpage
	end := start + perpage
	if start > len(listDtos) {
		start = len(listDtos)
	}
	if end > len(listDtos) {
		end = len(listDtos)
	}
	paginatedListDtos := listDtos[start:end]

	// Calculate total amounts
	_ = r.db.WithContext(ctx).Model(&entities.Payment{}).Select(query.BuildQuery(query.SelectTotalAmount)).
		Where(query.BuildQuery(query.WhereStoreID, query.WhereIsPaid), store.ID, entities.Paid).Scan(&totalPayment).Error

	_ = r.db.WithContext(ctx).Model(&entities.VehicleItem{}).Select(query.BuildQuery(query.SelectTotalPrice)).
		Where(query.BuildQuery(query.WhereStoreID), store.ID).Scan(&totalAmount).Error

	return &dtos.PaginatedDataWithAmount{
		Page:            int64(page),
		PerPage:         int64(perpage),
		Total:           totalCount,
		TotalPages:      totalPages,
		Rows:            paginatedListDtos,
		TotalAmount:     totalAmount.TotalAmount,
		PaidAmount:      totalPayment.TotalAmount,
		RemainingAmount: totalAmount.TotalAmount - totalPayment.TotalAmount,
	}, nil
}

func processVehicleItems(vehicleItems []entities.VehicleItem) ([]string, float64) {
	var names []string
	var totalPrice decimal.Decimal

	for _, item := range vehicleItems {
		names = append(names, item.Name)
		totalPrice = totalPrice.Add(item.Price)
	}
	return names, totalPrice.InexactFloat64()
}

func calculateDebt(totalPrice decimal.Decimal, payments []entities.Payment) (float64, float64) {
	var totalPaid decimal.Decimal
	for _, payment := range payments {
		totalPaid = totalPaid.Add(payment.Amount)
	}
	return totalPrice.Sub(totalPaid).InexactFloat64(), totalPaid.InexactFloat64()
}

func SortVehicleByRemainingAmount(dtoItems []dtos.ListVehicle) []dtos.ListVehicle {
	sort.SliceStable(dtoItems, func(i, j int) bool {
		if dtoItems[i].RemainingPrice == 0 && dtoItems[j].RemainingPrice > 0 {
			return false
		}
		if dtoItems[i].RemainingPrice > 0 && dtoItems[j].RemainingPrice == 0 {
			return true
		}
		return dtoItems[i].RemainingPrice > dtoItems[j].RemainingPrice
	})
	return dtoItems
}

// RestoreVehicle restores a soft-deleted vehicle and its related items
func (r *repository) RestoreVehicle(ctx context.Context, id string) error {
	tx := r.db.WithContext(ctx).Begin()

	// Check if vehicle exists and is deleted
	var vehicle entities.Vehicle
	err := tx.Unscoped().Where(query.BuildQuery(query.WhereID), id).Where("deleted_at IS NOT NULL").First(&vehicle).Error
	if err != nil {
		tx.Rollback()
		log.CreateLog(&entities.Log{
			Title:   "Vehicle Not Found",
			Message: "Vehicle not found or not deleted: " + err.Error(),
			Entity:  "vehicle",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.VehicleNotFound)
	}

	// Restore vehicle
	err = tx.Unscoped().Model(&entities.Vehicle{}).Where(query.BuildQuery(query.WhereID), id).Update("deleted_at", nil).Error
	if err != nil {
		tx.Rollback()
		log.CreateLog(&entities.Log{
			Title:   "Vehicle Restore Failed",
			Message: "Vehicle restore failed: " + err.Error(),
			Entity:  "vehicle",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.VehicleUpdateFailed)
	}

	// Restore vehicle items
	err = tx.Unscoped().Model(&entities.VehicleItem{}).Where(query.BuildQuery(query.WhereVehicleID), id).Update("deleted_at", nil).Error
	if err != nil {
		tx.Rollback()
		log.CreateLog(&entities.Log{
			Title:   "Vehicle Items Restore Failed",
			Message: "Vehicle items restore failed: " + err.Error(),
			Entity:  "vehicle",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.VehicleItemUpdateFailed)
	}

	// Restore payments
	err = tx.Unscoped().Model(&entities.Payment{}).Where(query.BuildQuery(query.WhereVehicleID), id).Update("deleted_at", nil).Error
	if err != nil {
		tx.Rollback()
		log.CreateLog(&entities.Log{
			Title:   "Payments Restore Failed",
			Message: "Payments restore failed: " + err.Error(),
			Entity:  "vehicle",
			Type:    "error",
			Ip:      state.GetCurrentUserIP(ctx),
			UserID:  state.GetCurrentUserID(ctx),
		})
		return errors.New(consts.PaymentUpdateFailed)
	}

	tx.Commit()
	return nil
}

// GetStoresWithDeletedVehicles returns only stores that have deleted vehicles
func (r *repository) GetStoresWithDeletedVehicles(ctx context.Context, key string) ([]dtos.GetStore, error) {
	var stores []entities.Store
	var result []dtos.GetStore

	// Get all stores with optional search filter
	q := r.db.WithContext(ctx)
	if key != "" {
		q = q.Where("name ILIKE ? OR phone_number ILIKE ?", "%"+key+"%", "%"+key+"%")
	}

	err := q.Find(&stores).Error
	if err != nil {
		return nil, errors.New(consts.StoreGetFailed)
	}

	// For each store, check if it has deleted vehicles
	for _, store := range stores {
		var count int64
		r.db.WithContext(ctx).Model(&entities.Vehicle{}).
			Unscoped().
			Where("deleted_at IS NOT NULL").
			Where(query.BuildQuery(query.WhereStoreID), store.ID).
			Count(&count)

		// Only include stores with deleted vehicles
		if count > 0 {
			dto := store.ToGetDto(ctx)

			// Get total amount and paid amount for deleted vehicles
			var totalPayment entities.TotalAmount
			_ = r.db.WithContext(ctx).Model(&entities.Payment{}).
				Unscoped().
				Select(query.BuildQuery(query.SelectTotalAmount)).
				Where("deleted_at IS NOT NULL").
				Where(query.BuildQuery(query.WhereStoreID, query.WhereIsPaid), store.ID, entities.Paid).
				Scan(&totalPayment).Error

			var totalAmount entities.TotalAmount
			_ = r.db.WithContext(ctx).Model(&entities.VehicleItem{}).
				Unscoped().
				Select(query.BuildQuery(query.SelectTotalPrice)).
				Where("deleted_at IS NOT NULL").
				Where(query.BuildQuery(query.WhereStoreID), store.ID).
				Scan(&totalAmount).Error

			dto.TotalAmount = totalAmount.TotalAmount
			dto.PaidAmount = totalPayment.TotalAmount
			dto.RemainingAmount = dto.TotalAmount - dto.PaidAmount
			dto.DeletedVehiclesCount = count

			result = append(result, dto)
		}
	}

	return result, nil
}

func (r *repository) GetDeletedVehiclesByStoreId(ctx context.Context, id string, page, perpage int, key string) (*dtos.PaginatedDataWithAmount, error) {
	var vehicles []entities.Vehicle
	var listDtos []dtos.ListVehicle
	var totalAmount, totalPayment entities.TotalAmount

	// Get all deleted vehicles for this store
	deletedVehiclesQuery := r.db.WithContext(ctx).Model(&entities.Vehicle{}).
		Order(query.OrderByCreatedAtDesc).
		Unscoped().                      // Include soft deleted records
		Where("deleted_at IS NOT NULL"). // Only get deleted records
		Where(query.BuildQuery(query.WhereStoreID), id)

	if key != "" {
		deletedVehiclesQuery = deletedVehiclesQuery.Where(
			query.BuildQuery(query.WherePersonNameOrPhonePlateILike),
			"%"+key+"%", "%"+key+"%", "%"+key+"%",
		)
	}

	err := deletedVehiclesQuery.Find(&vehicles).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.New(consts.VehicleGetFailed)
	}

	// Create DTOs and calculate RemainingAmount
	for _, vehicle := range vehicles {
		var vehicleItems []entities.VehicleItem
		r.db.WithContext(ctx).Model(&entities.VehicleItem{}).
			Unscoped(). // Include soft deleted records
			Where(query.BuildQuery(query.WhereVehicleID), vehicle.ID).
			Find(&vehicleItems)

		dtoItem := vehicle.FromListDto(ctx, vehicle)
		dtoItem.Items, dtoItem.TotalPrice = processVehicleItems(vehicleItems)

		// Query payments
		var payments []entities.Payment
		_ = r.db.WithContext(ctx).Model(&entities.Payment{}).
			Unscoped(). // Include soft deleted records
			Where(query.BuildQuery(query.WhereVehicleID, query.WhereIsPaid), vehicle.ID, entities.Paid).
			Find(&payments)

		dtoItem.RemainingPrice, _ = calculateDebt(decimal.NewFromFloat(dtoItem.TotalPrice), payments)
		listDtos = append(listDtos, dtoItem)
	}

	// Sort by RemainingAmount
	listDtos = SortVehicleByRemainingAmount(listDtos)

	// Calculate total counts before pagination
	totalCount := int64(len(listDtos))
	totalPages := int(math.Ceil(float64(totalCount) / float64(perpage)))

	// Paginate
	start := (page - 1) * perpage
	end := start + perpage
	if start > len(listDtos) {
		start = len(listDtos)
	}
	if end > len(listDtos) {
		end = len(listDtos)
	}
	paginatedListDtos := listDtos[start:end]

	// Calculate total amounts for deleted items
	_ = r.db.WithContext(ctx).Model(&entities.Payment{}).
		Unscoped(). // Include soft deleted records
		Select(query.BuildQuery(query.SelectTotalAmount)).
		Where("deleted_at IS NOT NULL").
		Where(query.BuildQuery(query.WhereStoreID, query.WhereIsPaid), id, entities.Paid).
		Scan(&totalPayment).Error

	_ = r.db.WithContext(ctx).Model(&entities.VehicleItem{}).
		Unscoped(). // Include soft deleted records
		Select(query.BuildQuery(query.SelectTotalPrice)).
		Where("deleted_at IS NOT NULL").
		Where(query.BuildQuery(query.WhereStoreID), id).
		Scan(&totalAmount).Error

	return &dtos.PaginatedDataWithAmount{
		Page:            int64(page),
		PerPage:         int64(perpage),
		Total:           totalCount,
		TotalPages:      totalPages,
		Rows:            paginatedListDtos,
		TotalAmount:     totalAmount.TotalAmount,
		PaidAmount:      totalPayment.TotalAmount,
		RemainingAmount: totalAmount.TotalAmount - totalPayment.TotalAmount,
	}, nil
}

func (r *repository) GetDeletedVehiclesBySlugName(ctx context.Context, name string, page, perpage int, key string) (*dtos.PaginatedDataWithAmount, error) {
	var vehicles []entities.Vehicle
	var listDtos []dtos.ListVehicle
	var totalAmount, totalPayment entities.TotalAmount

	// Find store by slug name
	var store entities.Store
	err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereSlugName), name).First(&store).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.New(consts.StoreNotFound)
	}

	// Get all deleted vehicles for this store
	deletedVehiclesQuery := r.db.WithContext(ctx).Model(&entities.Vehicle{}).
		Order(query.OrderByCreatedAtDesc).
		Unscoped().                      // Include soft deleted records
		Where("deleted_at IS NOT NULL"). // Only get deleted records
		Where(query.BuildQuery(query.WhereStoreID), store.ID)

	if key != "" {
		deletedVehiclesQuery = deletedVehiclesQuery.Where(
			query.BuildQuery(query.WherePersonNameOrPhonePlateILike),
			"%"+key+"%", "%"+key+"%", "%"+key+"%",
		)
	}

	err = deletedVehiclesQuery.Find(&vehicles).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.New(consts.VehicleGetFailed)
	}

	// Create DTOs and calculate RemainingAmount
	for _, vehicle := range vehicles {
		var vehicleItems []entities.VehicleItem
		r.db.WithContext(ctx).Model(&entities.VehicleItem{}).
			Unscoped(). // Include soft deleted records
			Where(query.BuildQuery(query.WhereVehicleID), vehicle.ID).
			Find(&vehicleItems)

		dtoItem := vehicle.FromListDto(ctx, vehicle)
		dtoItem.Items, dtoItem.TotalPrice = processVehicleItems(vehicleItems)

		// Query payments
		var payments []entities.Payment
		_ = r.db.WithContext(ctx).Model(&entities.Payment{}).
			Unscoped(). // Include soft deleted records
			Where(query.BuildQuery(query.WhereVehicleID, query.WhereIsPaid), vehicle.ID, entities.Paid).
			Find(&payments)

		dtoItem.RemainingPrice, _ = calculateDebt(decimal.NewFromFloat(dtoItem.TotalPrice), payments)
		listDtos = append(listDtos, dtoItem)
	}

	// Sort by RemainingAmount
	listDtos = SortVehicleByRemainingAmount(listDtos)

	// Calculate total counts before pagination
	totalCount := int64(len(listDtos))
	totalPages := int(math.Ceil(float64(totalCount) / float64(perpage)))

	// Paginate
	start := (page - 1) * perpage
	end := start + perpage
	if start > len(listDtos) {
		start = len(listDtos)
	}
	if end > len(listDtos) {
		end = len(listDtos)
	}
	paginatedListDtos := listDtos[start:end]

	// Calculate total amounts for deleted items
	_ = r.db.WithContext(ctx).Model(&entities.Payment{}).
		Unscoped(). // Include soft deleted records
		Select(query.BuildQuery(query.SelectTotalAmount)).
		Where("deleted_at IS NOT NULL").
		Where(query.BuildQuery(query.WhereStoreID, query.WhereIsPaid), store.ID, entities.Paid).
		Scan(&totalPayment).Error

	_ = r.db.WithContext(ctx).Model(&entities.VehicleItem{}).
		Unscoped(). // Include soft deleted records
		Select(query.BuildQuery(query.SelectTotalPrice)).
		Where("deleted_at IS NOT NULL").
		Where(query.BuildQuery(query.WhereStoreID), store.ID).
		Scan(&totalAmount).Error

	return &dtos.PaginatedDataWithAmount{
		Page:            int64(page),
		PerPage:         int64(perpage),
		Total:           totalCount,
		TotalPages:      totalPages,
		Rows:            paginatedListDtos,
		TotalAmount:     totalAmount.TotalAmount,
		PaidAmount:      totalPayment.TotalAmount,
		RemainingAmount: totalAmount.TotalAmount - totalPayment.TotalAmount,
	}, nil
}
