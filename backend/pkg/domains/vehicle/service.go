package vehicle

import (
	"context"

	"github.com/SametAvcii/kalkan-auto/pkg/dtos"
	"github.com/SametAvcii/kalkan-auto/pkg/entities"
)

type Service interface {
	GetVehicles(ctx context.Context, page, perpage int) (*dtos.PaginatedData, error)
	CreateVehicle(ctx context.Context, req dtos.CreateVehicle) error
	GetVehicleById(ctx context.Context, id string) (dtos.GetVehicle, error)
	UpdateVehicle(ctx context.Context, id string, req dtos.UpdateVehicle) error
	DeleteVehicle(ctx context.Context, id string) error
	RestoreVehicle(ctx context.Context, id string) error
	GetVehiclesByStoreId(ctx context.Context, id string, page, perpage int, key string) (*dtos.PaginatedDataWithAmount, error)
	GetVehiclesBySlugName(ctx context.Context, name string, page, perpage int, key string) (*dtos.PaginatedDataWithAmount, error)
	GetDeletedVehiclesByStoreId(ctx context.Context, id string, page, perpage int, key string) (*dtos.PaginatedDataWithAmount, error)
	GetDeletedVehiclesBySlugName(ctx context.Context, name string, page, perpage int, key string) (*dtos.PaginatedDataWithAmount, error)
	GetStoresWithDeletedVehicles(ctx context.Context, key string) ([]dtos.GetStore, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) GetVehicles(ctx context.Context, page, perpage int) (*dtos.PaginatedData, error) {
	return s.repository.GetVehicles(ctx, page, perpage)
}

func (s *service) CreateVehicle(ctx context.Context, req dtos.CreateVehicle) error {
	var vehicle entities.Vehicle
	vehicle.FromDto(ctx, req)
	return s.repository.CreateVehicle(ctx, &vehicle)
}

func (s *service) GetVehicleById(ctx context.Context, id string) (dtos.GetVehicle, error) {
	return s.repository.GetVehicleById(ctx, id)
}

func (s *service) UpdateVehicle(ctx context.Context, id string, req dtos.UpdateVehicle) error {

	return s.repository.UpdateVehicle(ctx, id, req)
}

func (s *service) DeleteVehicle(ctx context.Context, id string) error {
	return s.repository.DeleteVehicle(ctx, id)
}

func (s *service) GetVehiclesByStoreId(ctx context.Context, id string, page, perpage int, key string) (*dtos.PaginatedDataWithAmount, error) {
	return s.repository.GetVehiclesByStoreId(ctx, id, page, perpage, key)
}

func (s *service) GetVehiclesBySlugName(ctx context.Context, name string, page, perpage int, key string) (*dtos.PaginatedDataWithAmount, error) {
	return s.repository.GetVehiclesBySlugName(ctx, name, page, perpage, key)
}

func (s *service) GetDeletedVehiclesByStoreId(ctx context.Context, id string, page, perpage int, key string) (*dtos.PaginatedDataWithAmount, error) {
	return s.repository.GetDeletedVehiclesByStoreId(ctx, id, page, perpage, key)
}

func (s *service) GetDeletedVehiclesBySlugName(ctx context.Context, name string, page, perpage int, key string) (*dtos.PaginatedDataWithAmount, error) {
	return s.repository.GetDeletedVehiclesBySlugName(ctx, name, page, perpage, key)
}

func (s *service) RestoreVehicle(ctx context.Context, id string) error {
	return s.repository.RestoreVehicle(ctx, id)
}

func (s *service) GetStoresWithDeletedVehicles(ctx context.Context, key string) ([]dtos.GetStore, error) {
	return s.repository.GetStoresWithDeletedVehicles(ctx, key)
}
