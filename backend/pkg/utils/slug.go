package utils

import (
	"regexp"
	"strings"
)

func ReplaceTurkishChars(input string) string {
	replacer := strings.NewReplacer(
		"ç", "c",
		"ğ", "g",
		"ı", "i",
		"ö", "o",
		"ş", "s",
		"ü", "u",
		"Ç", "c",
		"Ğ", "g",
		"İ", "i",
		"Ö", "o",
		"Ş", "s",
		"Ü", "u",
	)
	return replacer.Replace(input)
}

func Slugify(input string) string {
	input = ReplaceTurkishChars(input)

	input = strings.ToLower(input)

	reg, _ := regexp.Compile("[^a-z0-9\\s-]+")
	input = reg.ReplaceAllString(input, "")

	input = strings.ReplaceAll(input, " ", "-")

	reg, _ = regexp.Compile("-+")
	input = reg.ReplaceAllString(input, "-")

	input = strings.Trim(input, "-")

	return input
}
