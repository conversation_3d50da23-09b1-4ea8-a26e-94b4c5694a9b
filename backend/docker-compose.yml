version: "3"
services:
  kalkan-db:
    image: "postgres:14.6"
    container_name: kalkan-db
    volumes:
      - kalkan_data:/var/lib/postgresql/data
    networks:
      - main
    restart: always
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_USER=kalkan-admin
      - POSTGRES_PASSWORD=T2q4Ylw0KRe1YG1f
      - POSTGRES_DB=kalkan
      - TZ="Europe/Istanbul"

  kalkan:
    build:
      context: .
      dockerfile: Dockerfile
    image: kalkan
    environment:
      - TZ="Europe/Istanbul"
    container_name: kalkan
    restart: always
    networks:
      - main
    volumes:
      - ./:/app
      - ./config.yaml:/app/config.yaml
    ports:
      - 8000:8000
    depends_on:
      - kalkan-db
  caddy:
    image: caddy:latest
    environment:
      - TZ="Europe/Istanbul"
    container_name: caddy
    restart: unless-stopped
    network_mode: "host"
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile
      - ../frontend/dist:/srv 
      - caddy_data:/data
      - caddy_config:/config

volumes:
  kalkan_data:
  caddy_config:
  caddy_data:

networks:
  main:
    name: main_network
    driver: bridge
