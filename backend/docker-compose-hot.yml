version: "3"
services:
  kalkan-db:
    image: "postgres:14.6"
    container_name: kalkan-db
    volumes:
      - kalkan_data:/var/lib/postgresql/data
    networks:
      - main
    restart: always
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_USER=kalkan-admin
      - POSTGRES_PASSWORD=T2q4Ylw0KRe1YG1f
      - POSTGRES_DB=kalkan
      - TZ="Europe/Istanbul"

  kalkan:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: kalkan
    environment:
      - TZ="Europe/Istanbul"
    container_name: kalkan
    restart: always
    networks:
      - main
    volumes:
      - ./:/app
      - ./config.yaml:/app/config.yaml
    ports:
      - 8000:8000
    depends_on:
      - kalkan-db

volumes:
  kalkan_data:

networks:
  main:
    name: main_network
    driver: bridge