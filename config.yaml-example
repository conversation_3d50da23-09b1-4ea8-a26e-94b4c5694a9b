app:
  name: kalkan-oto
  port: 8000
  host:
  jwt_secret: "jwt_secret"
  jwt_expire: 9000
database:
  host: kalkan-db
  port: 5432
  user: kalkan
  pass: kalkan
  name: kalkan-db
allows:
  methods:
    - GET
    - POST
    - PUT
    - PATCH
    - DELETE
    - OPTIONS
  headers:
    - Content-Type
    - Authorization
    - X-CSRF-Token
    - data-api-key
  origins:
    - http://localhost:8000
    - http://localhost:9000
    - http://localhost:4040
    - http://localhost:3000
    - http://localhost:5173
    - http://127.0.0.1:5173
    - http://127.0.0.1:3000